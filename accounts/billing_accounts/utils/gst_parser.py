import logging
import re
from abc import ABC, abstractmethod
from typing import Dict, Optional

from accounts.kyc.schemas import KYCAddress
from accounts.utils.common import get_test_mobile_number_for_otp

logger = logging.getLogger(__name__)


class GSTDetail:
    def __init__(
        self,
        data: Dict,
        gstin: str,
        legal_name: str,
        pincode: str,
        address: str,
        status: str,
    ):
        self.data = data
        self.gstin = gstin
        self.legal_name = legal_name
        self.pincode = pincode
        self.address = address
        self.status = status

    def __repr__(self):
        return f"<GSTDetail gstin={self.gstin}, status={self.status}, state={self.state_code}, pincode={self.pincode}>"

    def is_active(self) -> bool:
        return self.status.lower() == "active"

    @property
    def pan(self) -> str:
        return self.gstin[2:12]

    @property
    def state_code(self) -> str:
        return self.gstin[:2]

    @property
    def mobile(self) -> str:
        # NOTE: This is only for testing to bypass GST OTP verification.
        test_number = get_test_mobile_number_for_otp()
        if test_number:
            logger.info("Using test mobile number for GST OTP verification.")
            return test_number

        mobile = (
            self.data.get("contact_details", {})
            .get("principal", {})
            .get("mobile", "")
        )
        return mobile or ""

    def get_kyc_address(self) -> KYCAddress:
        address_details = self.data.get("address_details")
        if not address_details:
            logger.error(
                "Error parsing address details: No address details provided"
            )
            return KYCAddress()

        try:
            address = f"{address_details['floor']} {address_details['building_number']} {address_details['building_name']} {address_details['street']}"

            return KYCAddress(
                address=address,
                area=address_details["locality"],
                city=address_details["district"],
                state=address_details["state"],
                pin_code=address_details["pincode"],
            )
        except ValueError as e:
            logger.critical(f"Error parsing address details: {e}")
            return KYCAddress()


class BaseGSTParser(ABC):
    def __init__(self, data: Dict):
        self.data = data

    @abstractmethod
    def parse(self) -> GSTDetail:
        raise NotImplementedError("Subclass must implement this method")


class SurepassGSTParser(BaseGSTParser):
    def extract_address(self) -> Optional[str]:
        """Extracts address."""
        address = (
            self.data.get("contact_details", {})
            .get("principal", {})
            .get("address", "")
        )
        return address

    def extract_pincode(self, address: str) -> Optional[str]:
        """Extracts 6-digit pincode from address."""
        match = re.search(r"\b\d{6}\b", address)
        return match.group(0) if match else ""

    def parse(self) -> GSTDetail:
        address = self.extract_address()

        return GSTDetail(
            data=self.data,
            gstin=self.data["gstin"],
            legal_name=self.data["legal_name"],
            pincode=self.extract_pincode(address),
            address=address,
            status=self.data["gstin_status"],
        )
