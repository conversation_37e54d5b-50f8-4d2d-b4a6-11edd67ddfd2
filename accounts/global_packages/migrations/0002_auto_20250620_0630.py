# Generated by Django 3.2.18 on 2025-06-20 06:30

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('global_packages', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='globalpackagefeaturerates',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='globalpackagefeatures',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='globalpackagegroups',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='globalpackages',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='packagecategories',
            options={'ordering': ['created']},
        ),
    ]
