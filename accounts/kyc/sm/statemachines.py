import logging
import typing as t

from django.db import transaction

from statemachine import State, StateMachine

from accounts.kyc.enums import KYCModeEnum, KYCStateEnum, KYCStateStatusEnum
from accounts.kyc.exceptions import (
    DigilockerInitException,
    KycStateNotFoundException,
)
from accounts.kyc.models import KYC
from accounts.kyc.tasks import (
    download_gst_pdf_task,
    fetch_gst_info_for_kyc_task,
    fetch_pan_info_from_file_task,
)
from accounts.utils.api_services.surepass import SurepassApi

logger = logging.getLogger(__name__)


class KYCStateMachine(StateMachine):
    # states
    verification = State(value=KYCStateEnum.VERIFICATION.value, initial=True)
    gst_info = State(value=KYCStateEnum.GST_INFO.value)
    upload_pan = State(value=KYCStateEnum.UPLOAD_PAN.value)
    digilocker_pan = State(value=KYCStateEnum.DIGILOCKER_PAN.value)
    video_kyc = State(value=KYCStateEnum.VIDEO_KYC.value)
    e_sign = State(value=KYCStateEnum.E_SIGN.value)
    kyc_done = State(value=KYCStateEnum.KYC_DONE.value, final=True)

    # transitions
    # via Aadhaar mode
    transition_to_gst_info = gst_info.from_(
        verification,
        gst_info,
        upload_pan,
        digilocker_pan,
        cond=[
            "post_aadhaar_verification_cond",
        ],
    )
    transition_to_upload_pan = upload_pan.from_(
        verification,
        gst_info,
        upload_pan,
        digilocker_pan,
        cond=["post_aadhaar_verification_cond"],
    )
    transition_to_digilocker_pan = digilocker_pan.from_(
        verification,
        gst_info,
        upload_pan,
        digilocker_pan,
        cond=["post_aadhaar_verification_cond"],
    )

    # via Gst mode
    transition_to_video_kyc = video_kyc.from_(
        verification,
        gst_info,
        upload_pan,
        digilocker_pan,
        video_kyc,
        cond=[
            "transition_to_video_kyc_cond",
        ],
    )

    transition_video_kyc_to_e_sign = video_kyc.to(
        e_sign,
        cond=["not is_e_sign_skipped", "is_video_kyc_completed"],
    )
    transition_e_sign_to_kyc_done = e_sign.to(
        kyc_done, cond=["is_e_sign_completed"]
    )

    # not required:
    transition_video_kyc_to_kyc_done = video_kyc.to(
        kyc_done,
        cond=["is_e_sign_skipped", "is_video_kyc_completed"],
    )

    def __init__(
        self,
        initial_state: t.Optional[str] = None,
        kyc_mode: KYCModeEnum = KYCModeEnum.GST,
    ):
        self.kyc_mode = kyc_mode
        super().__init__()

        if initial_state:
            self.current_state = self.get_state_by_value(initial_state)

    def get_state_by_value(self, state_value: str):
        """Fetches a state by its value.

        Args:
            state_value (str): The value of the state to fetch.

        Raises:
            ValueError: If no state with the given value is found.
        """

        for state in self.states:
            if state.value == state_value:
                return state

        error_msg = (
            "[KYCStateMachine] No KYC state found with value: %s. Failed to instantiate KYCStateMachine."
            % state_value
        )
        logger.error(error_msg, title="KYC state not found", exc_info=True)
        raise ValueError(error_msg)

    def get_states_mapping(self):
        """Returns a mapping of state values.

        Returns:
            _type_: _description_
        """

        return {
            self.verification.value: {
                KYCModeEnum.GST.value: self.video_kyc.value,
                KYCModeEnum.AADHAAR.value: {
                    self.gst_info.value: self.video_kyc.value,
                    self.upload_pan.value: self.video_kyc.value,
                    self.digilocker_pan.value: self.video_kyc.value,
                },
            },
            self.video_kyc.value: self.e_sign.value,
            self.e_sign.value: self.kyc_done.value,
            self.kyc_done.value: None,
        }

    def is_kyc_mode_gst(self) -> bool:
        return self.kyc_mode == KYCModeEnum.GST

    def is_kyc_mode_aadhaar(self) -> bool:
        return self.kyc_mode == KYCModeEnum.AADHAAR

    def is_verification_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def are_states_failed(self, states: t.Tuple[str, ...]) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_any_state_completed(self, states: t.Tuple[str, ...]) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def post_aadhaar_verification_cond(self):
        print(
            self.are_states_failed(
                states=(
                    KYCStateEnum.GST_INFO.value,
                    KYCStateEnum.UPLOAD_PAN.value,
                    KYCStateEnum.DIGILOCKER_PAN.value,
                )
            )
        )
        return (
            self.is_kyc_mode_aadhaar()
            and self.is_verification_completed()
            and self.are_states_failed(
                states=(
                    KYCStateEnum.GST_INFO.value,
                    KYCStateEnum.UPLOAD_PAN.value,
                    KYCStateEnum.DIGILOCKER_PAN.value,
                )
            )
        )

    def is_gst_info_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_upload_pan_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_digilocker_pan_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_video_kyc_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_e_sign_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_kyc_done_completed(self) -> bool:
        # Override this method to implement the actual logic
        # For example: Determine from the Database
        return True

    def is_e_sign_skipped(self) -> bool:
        # Override this method to implement the actual logic
        # Future provision: This is a placeholder for any logic that determines if e_sign is skipped
        return False

    def transition_to_video_kyc_cond(self):
        return (
            self.is_kyc_mode_gst() and self.is_verification_completed()
        ) or (
            self.is_kyc_mode_aadhaar()
            and self.is_verification_completed()
            and (
                self.is_any_state_completed(
                    states=(
                        KYCStateEnum.GST_INFO.value,
                        KYCStateEnum.UPLOAD_PAN.value,
                        KYCStateEnum.DIGILOCKER_PAN.value,
                    )
                )
            )
            and self.are_states_failed(states=(KYCStateEnum.VIDEO_KYC.value,))
        )


class PersistedKYCStateMachine(KYCStateMachine):
    def __init__(self, kyc: KYC):
        self.kyc = kyc
        super().__init__(
            initial_state=self.kyc.current_state,
            kyc_mode=KYCModeEnum(self.kyc.mode),
        )

    @transaction.atomic
    def on_transition_to_video_kyc(self, data: t.Dict) -> t.Optional[t.Dict]:
        """
        This method initializes the video KYC and updates the KYC state to VIDEO_KYC.
        """
        api_res = SurepassApi().init_video_kyc(data)
        self.kyc.current_state = KYCStateEnum.VIDEO_KYC.value
        self.kyc.save()
        self.kyc.create_kyc_state(data=api_res)
        return api_res

    def after_transition_to_video_kyc(self):
        """
        This method marks the latest Video KYC state as completed.
        """
        self.kyc.mark_latest_state_as_completed(KYCStateEnum.VIDEO_KYC)

    @transaction.atomic
    def on_transition_to_gst_info(self, gst_number):
        """
        This method updates the KYC state to GST_INFO, creates a new state
        record, and schedules the fetch_gst_info_for_kyc_task to be executed
        asynchronously. It also sets the task ID in the latest state record.

        Args:
            gst_number (str): The GST number to fetch the data for.

        Returns:
            str: The task ID of the task that was created.
        """
        self.kyc.current_state = KYCStateEnum.GST_INFO.value
        self.kyc.save()
        self.kyc.create_kyc_state(data={})

        task = fetch_gst_info_for_kyc_task.apply_async(  # type: ignore
            args=[gst_number, self.kyc.pk, KYCStateEnum.GST_INFO.name],
            link=download_gst_pdf_task.si(
                gst_number,
                self.kyc.pk,
                KYCStateEnum.GST_INFO.name,
            ),  # type: ignore
        )
        self.kyc.get_latest_state(state=KYCStateEnum.GST_INFO).update_task_id(
            task.id
        )
        logger.info(
            f"[on_transition_to_gst_info] Scheduled fetch_gst_info_for_kyc_task for GST number: {gst_number} with task id: {task.id}"
        )
        return task.id

    @transaction.atomic
    def on_transition_to_upload_pan(self, s3_key: str) -> str:
        if self.kyc.current_state != KYCStateEnum.UPLOAD_PAN.value:
            self.kyc.current_state = KYCStateEnum.UPLOAD_PAN.value
            self.kyc.save()
        self.kyc.create_kyc_state(data={})
        task = fetch_pan_info_from_file_task.apply_async(  # type: ignore
            args=[s3_key, self.kyc.pk],
        )
        self.kyc.get_latest_state(state=KYCStateEnum.UPLOAD_PAN).update_task_id(
            task.id
        )
        logger.info(
            f"[on_transition_to_upload_pan] Scheduled fetch_pan_info_from_file_task task id: {task.id}"
        )
        return task.id

    @transaction.atomic
    def on_transition_to_digilocker_pan(self, redirect_url: str) -> dict:
        """This method initializes the digilocker and updates the KYC state to DIGILOCKER_PAN.

        Args:
            redirect_url (str): The redirect URL to be used by digilocker.

        Returns:
            dict: The result of the digilocker initialization.
        """
        if self.kyc.current_state != KYCStateEnum.DIGILOCKER_PAN.value:
            self.kyc.current_state = KYCStateEnum.DIGILOCKER_PAN.value
            self.kyc.save()
        self.kyc.create_kyc_state(data={})
        result = {}
        try:
            result = SurepassApi().init_digilocker_pan(
                redirect_url=redirect_url
            )
            logger.info(
                f"[on_transition_to_digilocker_pan] Initialized digilocker successfully. Result: {result}"
            )
        except DigilockerInitException as e:
            logger.error(
                f"[on_transition_to_digilocker_pan] Failed to initialize digilocker: {e}",
                exc_info=True,
            )
        return result

    def is_verification_completed(self) -> bool:
        """Checks if the verification state is completed.

        Returns:
            bool: True if the verification state is completed, False otherwise.
        """
        try:
            kyc_state = self.kyc.get_latest_state(
                state=KYCStateEnum.VERIFICATION
            )
            is_done = (
                kyc_state.data not in (None, {}) and kyc_state.is_completed()
            )
            if not is_done:
                logger.info(
                    f"[is_verification_completed] Verification state is not completed. Data: {kyc_state.data}"
                )
            return is_done
        except KycStateNotFoundException:
            return False

    def is_gst_info_completed(self) -> bool:
        """Checks if the gst_info state is completed.

        Returns:
            bool: True if the gst_info state is completed, False otherwise.
        """
        try:
            kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.GST_INFO)
            is_done = (
                kyc_state.data not in (None, {}) and kyc_state.is_completed()
            )
            if not is_done:
                logger.info(
                    f"[is_gst_info_completed] GST info state is not completed. Data: {kyc_state.data}"
                )
            return is_done
        except KycStateNotFoundException:
            return False

    def is_upload_pan_completed(self) -> bool:
        """Checks if the upload_pan state is completed.

        Returns:
            bool: True if the upload_pan state is completed, False otherwise.
        """
        try:
            kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.UPLOAD_PAN)
            is_done = (
                kyc_state.data not in (None, {}) and kyc_state.is_completed()
            )
            if not is_done:
                logger.info(
                    f"[is_upload_pan_completed] Upload pan state is not completed. Data: {kyc_state.data}"
                )
            return is_done
        except KycStateNotFoundException:
            return False

    def is_digilocker_pan_completed(self) -> bool:
        """Checks if the digilocker_pan state is completed.

        Returns:
            bool: True if the digilocker_pan state is completed, False otherwise.
        """
        try:
            kyc_state = self.kyc.get_latest_state(
                state=KYCStateEnum.DIGILOCKER_PAN
            )
            is_done = (
                kyc_state.data not in (None, {}) and kyc_state.is_completed()
            )
            if not is_done:
                logger.info(
                    f"[is_digilocker_pan_completed] Digilocker pan state is not completed. Data: {kyc_state.data}"
                )
            return is_done
        except KycStateNotFoundException:
            return False

    def is_video_kyc_completed(self) -> bool:
        """Checks if the video_kyc state is completed.

        Returns:
            bool: True if the video_kyc state is completed, False otherwise.
        """
        try:
            kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.VIDEO_KYC)
            is_done = (
                kyc_state.data not in (None, {}) and kyc_state.is_completed()
            )
            if not is_done:
                logger.info(
                    f"[is_video_kyc_completed] Video KYC state is not completed. Data: {kyc_state.data}"
                )
            return is_done
        except KycStateNotFoundException:
            return False

    def is_e_sign_completed(self) -> bool:
        """Checks if the e_sign state is completed.

        Returns:
            bool: True if the e_sign state is completed, False otherwise.
        """
        try:
            kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.E_SIGN)
            is_done = (
                kyc_state.data not in (None, {}) and kyc_state.is_completed()
            )
            if not is_done:
                logger.info(
                    f"[is_e_sign_completed] E sign state is not completed. Data: {kyc_state.data}"
                )
            return is_done
        except KycStateNotFoundException:
            return False

    def is_kyc_done_completed(self) -> bool:
        """Checks if the kyc_done state is completed.

        Returns:
            bool: True if the kyc_done state is completed, False otherwise.
        """
        try:
            kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.E_SIGN)
            is_done = (
                kyc_state.data not in (None, {}) and kyc_state.is_completed()
            )
            if not is_done:
                logger.info(
                    f"[is_kyc_done_completed] KYC done state is not completed. Data: {kyc_state.data}"
                )
            return is_done
        except KycStateNotFoundException:
            return False

    def is_marked_as_failed(self) -> bool:
        """Checks if the KYC is marked as failed.

        Returns:
            bool: True if the KYC is marked as failed, False otherwise.
        """

        return self.kyc.is_marked_as_failed()

    def mark_as_failed(self, failure_reason: str):
        """Marks the KYC as failed and updates the latest KYC state as failed with the provided reason.

        Args:
            failure_reason (str): The reason for marking the KYC as failed.

        Raises:
            Exception: If an error occurs while marking the KYC as failed.
        """

        self.kyc.mark_as_failed(failure_reason=failure_reason)

    def are_states_failed(self, states: t.Tuple[str, ...]) -> bool:
        """Checks if the provided states are failed.

        Args:
            states (t.Tuple[str, ...]): The states to check.

        Returns:
            bool: True if (all the provided states are failed) OR no states exists in Database, False otherwise.
        """

        kyc_states = self.kyc.states.filter(state__in=states).values("status")
        return all(
            kyc_state["status"] == KYCStateStatusEnum.FAILED.value
            for kyc_state in kyc_states
        )

    def is_any_state_completed(self, states: t.Tuple[str, ...]) -> bool:
        """Checks if any of the provided states are completed.

        Args:
            states (t.Tuple[str, ...]): The states to check.

        Returns:
            bool: True if any of the provided states are completed, False otherwise.
        """

        print("###", self.kyc.states.values("state", "status"))

        kyc_states = self.kyc.states.filter(state__in=states).values("status")
        return any(
            kyc_state["status"] == KYCStateStatusEnum.COMPLETED.value
            for kyc_state in kyc_states
        )
