import logging
import typing as t

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from statemachine.exceptions import TransitionNotAllowed

from accounts.exceptions import BaseException
from accounts.kyc.exceptions import TransitionNotAllowedException
from accounts.kyc.mixins import KycObjMixin
from accounts.kyc.models import KYC
from accounts.kyc.serializers.video_kyc import VideoKycInitSerializer
from accounts.kyc.sm.statemachines import PersistedKYCStateMachine
from accounts.utils.permissions.billing_account import BillingAccountPermission

logger = logging.getLogger(__name__)


class VideoKycInitView(KycObjMixin, APIView):
    permission_classes = [BillingAccountPermission]
    serializer_class = VideoKycInitSerializer

    def transition(self, kyc: KYC, video_kyc_data: t.Dict) -> str:
        sm = PersistedKYCStateMachine(kyc)
        try:
            api_data = sm.transition_to_video_kyc(video_kyc_data)
            return api_data
        except TransitionNotAllowed as error:
            logger.error(
                f"Transition not allowed for kyc_id: {kyc.id}, error: {error}",
                exc_info=True,
            )
            raise TransitionNotAllowedException() from error

    def post(self, request, *args, **kwargs):
        logger.info(f"Video KYC request: {request.data}")
        try:
            kyc = self.get_object(request)
            serializer = self.serializer_class(data=request.data)
            serializer.is_valid(raise_exception=True)
            api_data = self.transition(kyc, serializer.data)  # type: ignore
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "Liveness initialized successfully.",
                    "data": api_data,
                }
            )
        except BaseException as error:
            logger.critical(
                f"[VideoKycInitView] Failed to initialize video KYC: {error}",
                exc_info=True,
            )
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                error.get_http_status_code(),
            )
