import logging

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from statemachine.exceptions import TransitionNotAllowed

from accounts.exceptions import BaseException
from accounts.kyc.exceptions import (
    DigilockerInitException,
    DigilockerInvalidRedirectUrlException,
    TransitionNotAllowedException,
)
from accounts.kyc.mixins import KycObjMixin
from accounts.kyc.models import KYC
from accounts.kyc.sm.statemachines import PersistedKYCStateMachine
from accounts.utils.permissions.billing_account import BillingAccountPermission

logger = logging.getLogger(__name__)


class DigilockerInitView(APIView, KycObjMixin):
    permission_classes = [BillingAccountPermission]

    def validate_payload(self, request):
        """Validates the request payload.

        Args:
            request (Request): The request object.

        Raises:
            DigilockerInvalidRedirectUrlException: If the redirect_url is invalid.
        """

        redirect_url = request.data.get("redirect_url")

        if (
            (not redirect_url)
            or (not isinstance(redirect_url, str))
            or (not str(redirect_url).strip())
        ):
            raise DigilockerInvalidRedirectUrlException()

    def transition(self, kyc: KYC, redirect_url: str):
        sm = PersistedKYCStateMachine(kyc)
        try:
            return sm.transition_to_digilocker_pan(redirect_url)
        except TransitionNotAllowed as error:
            logger.error(
                f"Transition not allowed for kyc_id: {kyc.id}, state: {kyc.current_state}, error: {error}",
                exc_info=True,
            )
            raise TransitionNotAllowedException(kyc=kyc) from error

    def post(self, request, *args, **kwargs):
        try:
            self.validate_payload(request)
            redirect_url = str(request.data["redirect_url"]).strip()
            kyc: KYC = self.get_object(request)
            result = self.transition(kyc=kyc, redirect_url=redirect_url)
            if not result:
                raise DigilockerInitException(
                    "Digilocker authorization failed. Please try again."
                )
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "Digilocker initialized successfully.",
                    "data": {
                        "client_id": result["client_id"],
                        "url": result["url"],
                        "expiry_seconds": result.get("expiry_seconds"),
                        "token": result.get("token"),
                    },
                },
                status=status.HTTP_200_OK,
            )
        except BaseException as error:
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                status=error.get_http_status_code(),
            )
