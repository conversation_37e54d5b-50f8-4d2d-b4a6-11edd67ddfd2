import logging

from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response

from accounts.exceptions import BaseException
from accounts.kyc.enums import KYCStatusEnum
from accounts.kyc.handlers import <PERSON>ycHand<PERSON>
from accounts.kyc.mixins import KycObjMixin
from accounts.kyc.serializers.e_sign import ESignInitSerializer
from accounts.utils.permissions.billing_account import BillingAccountPermission

logger = logging.getLogger(__name__)


class ESignView(KycObjMixin, GenericAPIView):
    permission_classes = [BillingAccountPermission]
    serializer_class = ESignInitSerializer

    def get(self, request, *args, **kwargs):
        try:
            kyc = self.get_object(
                request,
                kyc_status=KYCStatusEnum.PENDING.value,
            )

            handler = KycHandler(kyc)
            serializer = self.serializer_class()
            data = handler.fill_e_sign_data(serializer.data)
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "E-sign initialized successfully.",
                    "data": data,
                }
            )

        except BaseException as error:
            logger.critical(
                f"[ESignView] Failed to initialize e-sign: {error}",
                exc_info=True,
            )
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                error.get_http_status_code(),
            )
