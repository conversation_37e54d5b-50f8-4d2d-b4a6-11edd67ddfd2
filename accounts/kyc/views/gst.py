import logging

from django.db import transaction

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from statemachine.exceptions import TransitionNotAllowed

from accounts.billing_accounts.utils.billing_account import is_valid_gst_no
from accounts.billing_accounts.utils.gst_parser import GSTDetail
from accounts.exceptions import BaseException, ServerError
from accounts.kyc.cache_handler import GstCacheHandler
from accounts.kyc.enums import KYCStateEnum
from accounts.kyc.exceptions import (
    InvalidGstFormatException,
    InvalidOtpException,
    NonOTPAttemptExceeded,
    TransitionNotAllowedException,
)
from accounts.kyc.handlers import KycHandler
from accounts.kyc.mixins import KycObjMixin
from accounts.kyc.models import KYC
from accounts.kyc.otp import GstOtpHandler
from accounts.kyc.sm.statemachines import PersistedKYCStateMachine
from accounts.kyc.tasks import download_gst_pdf_task
from accounts.kyc.validations import validate_existing_kyc
from accounts.utils.api_services.surepass_helpers import get_gst_data
from accounts.utils.common import mask_number
from accounts.utils.permissions.billing_account import BillingAccountPermission

logger = logging.getLogger(__name__)


class BaseGstView(APIView):
    permission_classes = [BillingAccountPermission]

    def validate_gst(self, gst_number: str):
        if not gst_number or not is_valid_gst_no(gst_number):
            raise InvalidGstFormatException()

    def error_response(self, error: BaseException) -> Response:
        logger.info(f"Error: {error} ", exc_info=True)
        return Response(
            {
                "status": "error",
                "code": error.get_error_code(),
                "message": str(error),
                "errors": error.get_errors(),
            },
            error.get_http_status_code(),
        )


class GstSendOtpView(BaseGstView):
    def post(self, request, *args, **kwargs):
        """
        Handle the sending of OTP for KYC verification.
        """
        logger.info("Request data: %s", request.data)
        try:
            validate_existing_kyc(request.billing_account)
            self.validate_gst(request.data.get("gst_number"))
            gst_number = request.data["gst_number"]
            otp_handler = GstOtpHandler()
            # sets up otp_cache_handler and api_service
            otp_handler.setup_send_otp(gst_number)
            # raises OTPMaxAttemptException if max attempts reached
            otp_handler.validate_max_send_attempts()
            # raises ExternalApiDependencyFailedException if GST number is invalid
            gst_data: GSTDetail = get_gst_data(gst_number)
            # sends OTP to the phone number
            otp_handler.send_otp(gst_data.mobile)
            masked_number = mask_number(gst_data.mobile)
            logger.info(
                f"OTP sent successfully on the phone number linked to GST number. masked_phone_number: {masked_number}"
            )
            return Response(
                {
                    "message": "OTP sent successfully on the phone number linked to GST number.",
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "data": {"masked_phone_number": masked_number},
                },
                status=status.HTTP_200_OK,
            )
        except ValueError as error:
            logger.error(f"ValueError: {error} ", exc_info=True)
            raise ServerError()  # raising server error!!
        except BaseException as error:
            return self.error_response(error)


class GstVerifyOtpView(BaseGstView):
    def validate_payload(self, request):
        self.validate_gst(request.data.get("gst_number"))
        if not request.data.get("otp"):
            raise InvalidGstFormatException()

    def get_gst_number(self, request):
        return request.data["gst_number"]

    def get_otp(self, request):
        return request.data["otp"]

    def verify_otp(self, request):
        gst_number = self.get_gst_number(request)
        otp_handler = GstOtpHandler()
        # sets up otp_cache_handler
        otp_handler.setup_verify_otp(gst_number)
        # raises OTPMaxAttemptException if max attempts reached
        otp_handler.validate_max_verification_attempts()
        otp = request.data["otp"]
        # raises InvalidOtpException if OTP is not provided
        # raises OtpDataNotFoundException if OTP data not found or expired
        otp_handler.verify_otp(otp)
        logger.info(f"OTP verified successfully for {gst_number}")

    def init_kyc(self, request) -> str:
        gst_number = self.get_gst_number(request)
        with transaction.atomic():
            kyc = KycHandler.init_gst_kyc(
                request.billing_account, get_gst_data(gst_number).data
            )
            task = download_gst_pdf_task.apply_async(  # type: ignore
                args=[gst_number, kyc.id, KYCStateEnum.VERIFICATION.name],
                kwargs={"fail_kyc": True},
            )
            logger.info(
                f"Task created for GST number: {gst_number} with task id: {task.id}"
            )
            kyc.get_latest_state(
                state=KYCStateEnum.VERIFICATION
            ).update_task_id(task.id)
        return task.id

    def post(self, request, *args, **kwargs):
        """
        Handle the verification of OTP for KYC verification.
        """
        logger.info("Request data: %s", request.data)
        try:
            validate_existing_kyc(request.billing_account)
            self.validate_payload(request)
            self.verify_otp(request)
            task_id = self.init_kyc(request)
            return Response(
                {
                    "status": "success",
                    "code": 200,
                    "message": "OTP verified successfully.",
                    "data": {"task_id": task_id},
                }
            )
        except ValueError as error:
            logger.error(f"ValueError: {error} ", exc_info=True)
            raise ServerError()  # raising server error!!
        except BaseException as error:
            return self.error_response(error)


class GstNonOtpView(BaseGstView, KycObjMixin):
    def validate_gst(self, gst_number: str):
        try:
            super().validate_gst(gst_number)
        except InvalidGstFormatException as err:
            raise InvalidGstFormatException(
                "Invalid GST number. Please enter a correct GST or choose PAN KYC."
            ) from err

    def transition(self, kyc: KYC, gst_number: str) -> str:
        sm = PersistedKYCStateMachine(kyc)
        try:
            task_id = sm.transition_to_gst_info(gst_number)
            return task_id
        except TransitionNotAllowed as error:
            logger.error(
                f"Transition not allowed for kyc_id: {kyc.id}, error: {error}",
                exc_info=True,
            )
            raise TransitionNotAllowedException() from error

    def post(self, request, *args, **kwargs):
        logger.info("GstNonOtpView POST request.data: %s" % request.data)
        try:
            self.validate_gst(request.data.get("gst_number"))
            gst_number = request.data["gst_number"]
            cache = GstCacheHandler(gst_number)
            if not cache.non_otp_attempt_allowed():
                raise NonOTPAttemptExceeded()
            kyc = self.get_object(request)
            cache.incr_non_otp_attempts()
            task_id = self.transition(kyc, gst_number)
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "GST number without OTP saved successfully.",
                    "data": {"task_id": task_id},
                },
                status=status.HTTP_200_OK,
            )
        except BaseException as error:
            return self.error_response(error)
