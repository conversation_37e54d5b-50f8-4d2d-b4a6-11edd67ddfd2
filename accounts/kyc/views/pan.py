import logging
import typing as t

from django.core.files.uploadedfile import UploadedFile

from kyc.cache_handler import PanCacheHandler
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from statemachine.exceptions import TransitionNotAllowed
from utils.aws.s3 import S3Client

from accounts.exceptions import (
    BaseException,
)
from accounts.kyc.exceptions import (
    InvalidPanUploadException,
    PanNotFoundException,
    TransitionNotAllowedException,
)
from accounts.kyc.mixins import KycObjMixin
from accounts.kyc.models import KYC
from accounts.kyc.sm.statemachines import PersistedKYCStateMachine
from accounts.utils.permissions.billing_account import BillingAccountPermission

logger = logging.getLogger(__name__)


class PanUploadView(APIView, KycObjMixin):
    permission_classes = [BillingAccountPermission]

    def validate_payload(self, request):
        """Validates the request payload.

        Args:
            request (Request): The request object.

        Raises:
            InvalidPanUploadException: If the payload is invalid.
        """

        s3_key = request.data.get("s3_key")

        if (
            (not s3_key)
            or (not isinstance(s3_key, str))
            or (not str(s3_key).strip())
        ):
            raise InvalidPanUploadException(
                "No or invalid s3_key provided. Expected as required string."
            )

    def transition(self, kyc: KYC, s3_key: str):
        sm = PersistedKYCStateMachine(kyc)
        try:
            task_id = sm.transition_to_upload_pan(s3_key)
            return task_id
        except TransitionNotAllowed as error:
            logger.error(
                f"Transition not allowed for kyc_id: {kyc.id}, error: {error}",
                exc_info=True,
            )
            raise TransitionNotAllowedException() from error

    def validate_s3_key_exists(self, s3_key: str):
        """Validates if the AWS S3 key exists.

        Args:
            s3_key (str): The S3 key.

        Raises:
            PanNotFoundException: If the AWS S3 key does not exist.
        """

        s3_client = S3Client()

        try:
            s3_client.head_object(s3_key)
        except Exception as err:
            raise PanNotFoundException(
                "AWS S3 key: %s does not exist." % s3_key
            ) from err

    def post(self, request, *args, **kwargs):
        try:
            self.validate_payload(request)
            s3_key = str(request.data["s3_key"]).strip()
            kyc: KYC = self.get_object(request)
            pan_cache_handler = PanCacheHandler(key=kyc.id)
            pan_cache_handler.validate_pan_upload_attempts()
            self.validate_s3_key_exists(s3_key)
            pan_cache_handler.incr_pan_upload_attempts()
            task_id = self.transition(kyc, s3_key)
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "PAN upload process started successfully.",
                    "data": {"task_id": task_id},
                },
                status=status.HTTP_200_OK,
            )
        except BaseException as error:
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                status=error.get_http_status_code(),
            )
