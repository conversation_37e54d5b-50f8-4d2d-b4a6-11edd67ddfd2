import logging
from dataclasses import asdict

from django.db import transaction

from kyc.enums import KYCStateEnum
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.exceptions import (
    BaseException,
)
from accounts.kyc.cache_handler import <PERSON>adhaarCacheHandler
from accounts.kyc.exceptions import (
    InvalidAadhaarClientIdException,
    InvalidAadhaarNumberException,
    InvalidAadhaarNumberOtpException,
    OtpDataNotFoundException,
)
from accounts.kyc.handlers import KycHandler
from accounts.kyc.models import KYC
from accounts.kyc.schemas import AadhaarDetail, AadhaarSendOtpApiResponseData
from accounts.kyc.tasks import download_aadhaar_pdf_task
from accounts.kyc.validations import validate_existing_kyc
from accounts.utils.api_services.surepass import SurepassApi
from accounts.utils.kyc import is_valid_aadhaar_number
from accounts.utils.permissions.billing_account import BillingAccountPermission

logger = logging.getLogger(__name__)


class BaseAadhaarView(APIView):
    permission_classes = [BillingAccountPermission]

    def validate_aadhaar_number(self, aadhaar_number: str) -> str:
        """Validates the Aadhaar number.

        Args:
            aadhaar_number (str): The Aadhaar number to be validated.

        Raises:
            InvalidAadhaarNumberException: If the Aadhaar number is invalid.

        Returns:
            str: The validated and cleaned Aadhaar number.
        """

        if (not aadhaar_number) or (
            not is_valid_aadhaar_number(aadhaar_number)
        ):
            raise InvalidAadhaarNumberException()
        return aadhaar_number.replace(" ", "")


class AadhaarSendOtpView(BaseAadhaarView):
    def post(self, request, *args, **kwargs):
        """Sends OTP for Aadhaar number verification."""

        logger.info("AadhaarSendOtpView POST request.data: %s" % request.data)

        try:
            validate_existing_kyc(request.billing_account)
            aadhaar_number = self.validate_aadhaar_number(
                request.data.get("aadhaar_number")
            )
            aadhaar_send_otp_api_response_data: (
                AadhaarSendOtpApiResponseData
            ) = SurepassApi().send_otp_for_aadhaar(
                aadhaar_number=aadhaar_number
            )
            AadhaarCacheHandler(key=aadhaar_number).save_otp_data(
                aadhaar_client_id=aadhaar_send_otp_api_response_data.client_id
            )
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "OTP sent successfully on the phone number linked to Aadhaar number.",
                    "data": None,
                },
                status=status.HTTP_200_OK,
            )
        except BaseException as error:
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                status=error.get_http_status_code(),
            )


class AadhaarVerifyOtpView(BaseAadhaarView):
    def validate_aadhaar_otp(self, otp: str) -> str:
        """Validates the Aadhaar OTP.

        Args:
            otp (str): The Aadhaar OTP to be validated.

        Raises:
            InvalidAadhaarNumberOtpException: If the Aadhaar OTP is invalid.

        Returns:
            str: The validated Aadhaar OTP.
        """

        if (not otp) or (not str(otp).isdigit()) or (len(str(otp)) != 6):
            raise InvalidAadhaarNumberOtpException()
        return str(otp)

    def validate_payload(self, request):
        """Validates the request payload."""

        self.aadhaar_number = self.validate_aadhaar_number(
            request.data.get("aadhaar_number")
        )
        self.aadhaar_otp = self.validate_aadhaar_otp(request.data.get("otp"))

    def verify_otp(self, request):
        """Verifies OTP for Aadhaar number verification.

        Args:
            request (Request): The request object.

        Raises:
            OtpDataNotFoundException: If the OTP data is not found in cache.
            InvalidAadhaarClientIdException: If the Aadhaar client id is not found in cache.
        """

        aadhaar_cache_handler = AadhaarCacheHandler(key=self.aadhaar_number)
        aadhaar_cache_data = aadhaar_cache_handler.fetch_otp_data()

        if not aadhaar_cache_data:
            raise OtpDataNotFoundException()

        aadhaar_cache_handler.validate_verification_attempts()
        aadhaar_client_id = aadhaar_cache_data.get("client_id")

        if not aadhaar_client_id:
            error_msg = (
                "No or invalid Aadhaar client id found in cache for aadhaar_number: %s, aadhaar_client_id: %s"
                % (self.aadhaar_number, aadhaar_client_id)
            )
            logger.critical(
                f"[AadhaarVerifyOtpView] {error_msg}",
                title="No or invalid Aadhaar client id in cache",
                exc_info=True,
            )
            raise InvalidAadhaarClientIdException(error_msg)

        aadhaar_cache_handler.incr_verification_attempts()
        self.aadhaar_client_id = str(aadhaar_client_id)
        self.aadhaar_detail: (
            AadhaarDetail
        ) = SurepassApi().verify_otp_for_aadhaar(
            aadhaar_client_id=self.aadhaar_client_id,
            aadhaar_otp=self.aadhaar_otp,
        )

    def init_kyc(self, request) -> str:
        """Initializes Aadhaar KYC.

        Args:
            request (Request): The request object.

        Returns:
            str: The task id.
        """

        with transaction.atomic():
            kyc: KYC = KycHandler.init_aadhaar_kyc(
                billing_account=request.billing_account,
                aadhaar_data=asdict(self.aadhaar_detail),
            )
            task = download_aadhaar_pdf_task.apply_async(  # type: ignore
                args=[self.aadhaar_number, self.aadhaar_client_id, kyc.id],
            )
            logger.info(
                f"[AadhaarVerifyOtpView] Task created for Aadhaar number: {self.aadhaar_number}, aadhaar_client_id: {self.aadhaar_client_id}, task_id: {task.id}"
            )
            kyc.get_latest_state(
                state=KYCStateEnum.VERIFICATION
            ).update_task_id(task.id)
        return task.id

    def post(self, request, *args, **kwargs):
        """Verifies OTP for Aadhaar number verification."""

        logger.info("AadhaarVerifyOtpView POST request.data: %s" % request.data)

        try:
            validate_existing_kyc(request.billing_account)
            self.validate_payload(request)
            self.verify_otp(request)
            task_id = self.init_kyc(request)
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "OTP verified successfully.",
                    "data": {"task_id": task_id},
                }
            )
        except BaseException as error:
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                status=error.get_http_status_code(),
            )
