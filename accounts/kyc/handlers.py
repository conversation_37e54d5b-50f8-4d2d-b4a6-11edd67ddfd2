import io
import logging
import mimetypes
import typing as t
from dataclasses import asdict
from datetime import datetime
from urllib.parse import urlparse

from django.conf import settings

from accounts.billing_accounts.enums import DocTypeEnums
from accounts.billing_accounts.models import BillingAccountDocs, BillingAccounts
from accounts.billing_accounts.utils.gst_parser import SurepassGSTParser
from accounts.cafs.models import Cafs
from accounts.kyc.enums import KYCModeEnum, KYCStateEnum
from accounts.kyc.exceptions import (
    KycDocNotFoundException,
    KycFailedException,
    KycStateNotFoundException,
)
from accounts.kyc.models import KYC
from accounts.kyc.parsers import A<PERSON>haarParser
from accounts.kyc.schemas import PanDetail
from accounts.utils.api_services.file_service import FileService
from accounts.utils.api_services.surepass import SurepassApi
from accounts.utils.aws.s3 import S3Client
from config.constants import MIMETYPE_JPG

logger = logging.getLogger(__name__)


class KycHandler:
    FILE_DOWNLOAD_TIMEOUT = 10  # todo: fetch from settings
    S3_PREFIX = "companydoc"

    def __init__(self, kyc: KYC):
        self.kyc = kyc
        self.file_service = FileService()
        self.s3_client = S3Client(settings.AWS_STORAGE_BUCKET_NAME)

    @classmethod
    def init_gst_kyc(
        cls, billing_account: BillingAccounts, gst_data: t.Dict
    ) -> KYC:
        """Initializes a GST KYC.

        Args:
            billing_account (BillingAccounts): The billing account.
            gst_data (t.Dict): The GST data.

        Returns:
            KYC: The initialized KYC.
        """

        logger.info(
            f"[KycHandler] Initializing GST KYC for billing account (id: {billing_account.id})"
        )

        kyc = KYC.initialize_kyc(
            mode=KYCModeEnum.GST,
            billing_account=billing_account,
            data=gst_data,
        )
        return kyc

    @classmethod
    def init_aadhaar_kyc(
        cls, billing_account: BillingAccounts, aadhaar_data: t.Dict
    ) -> KYC:
        """Initializes Aadhaar KYC.

        Args:
            billing_account (BillingAccounts): The billing account.
            aadhaar_data (t.Dict): The Aadhaar data.

        Returns:
            KYC: The initialized KYC.
        """

        logger.info(
            f"[KycHandler] Initializing Aadhaar KYC for billing account (id: {billing_account.id})"
        )

        kyc = KYC.initialize_kyc(
            mode=KYCModeEnum.AADHAAR,
            billing_account=billing_account,
            data=aadhaar_data,
        )
        return kyc

    def generate_s3_key(
        self, extension: str, file_name: t.Optional[str] = None
    ) -> str:
        """Generates an S3 key for the given extension and file name.

        Args:
            extension (str): The extension of the file.
            file_name (t.Optional[str], optional): The file name. Defaults to None.

        Returns:
            str: The generated S3 key.
        """

        if not file_name:
            file_name = str(int(datetime.now().timestamp()))

        return f"{self.S3_PREFIX}/{file_name}.{extension}"

    def get_file_name(self, url: str) -> str:
        parsed_url = urlparse(url)
        path = parsed_url.path
        file_name = path.split("/")[-1]
        return file_name

    def upload_file_to_s3(self, file_data: io.BytesIO, extension: str):
        key = self.generate_s3_key(extension=extension)
        try:
            self.s3_client.upload_file_obj(file_data, key)
        except Exception as err:
            raise KycFailedException(message="S3 file upload failed.") from err
        return key

    def download_file_from_s3(self, s3_key: str):
        try:
            return self.s3_client.download(s3_key)
        except Exception as err:
            raise KycFailedException(
                message="S3 file download failed."
            ) from err

    def copy_file_within_s3(self, file_source: str, file_destination: str):
        try:
            return self.s3_client.copy_file_object(
                file_source=file_source, file_destination=file_destination
            )
        except Exception as err:
            raise KycFailedException(message="S3 copy file failed.") from err

    def create_doc(
        self,
        doc_name: str,
        doc_ext: str,
        s3_path: str,
        doc_type: DocTypeEnums,
        doc_number: t.Optional[str] = None,
    ) -> BillingAccountDocs:
        return BillingAccountDocs.create_doc(
            self.kyc.billing_account,
            self.kyc,
            s3_path,
            doc_type,
            doc_name,
            doc_ext,
            doc_number,
        )

    def download_file_from_url(
        self,
        url: str,
        timeout: t.Optional[int] = FILE_DOWNLOAD_TIMEOUT,
        output_path: t.Optional[str] = None,
    ) -> t.Union[str, io.BytesIO]:
        """
        Downloads a file from the given URL and saves it to the specified output path or returns it in memory.

        Args:
            url (str): The URL to download the file from.
            timeout (t.Optional[int], optional): The timeout for the request in seconds. Defaults to FILE_DOWNLOAD_TIMEOUT.
            output_path (t.Optional[str], optional): The path where the file should be saved. If None, the file is returned in memory. Defaults to None.

        Returns:
            t.Union[str, io.BytesIO]: The path to the saved file if output_path is provided, otherwise a BytesIO object containing the file content.
        """

        logger.info(
            f"[KycHandler] Downloading file from URL: {url} with timeout: {timeout} and output path: {output_path}"
        )

        response = self.file_service.get_file_from_url(url=url, timeout=timeout)

        if output_path:
            with open(output_path, "wb") as f:
                f.write(response.content)
            return output_path
        return io.BytesIO(response.content)

    def copy_file_from_external_source_to_s3(
        self, file_url: str, resource_number: str, doc_type: DocTypeEnums
    ):
        """Downloads the file from the given external URL, uploads it to S3 and creates a document for the KYC.

        Args:
            file_url (str): The URL to download the file from.
            resource_number (str): The resource number.
            doc_type (DocTypeEnums): The type of document.
        """

        logger.info(
            f"[KycHandler] Processing file for resource_number: {resource_number}, doc_type: {doc_type}, file_url: {file_url}"
        )

        file_data: io.BytesIO = self.download_file_from_url(url=file_url)  # type: ignore
        file_name_with_ext = self.get_file_name(url=file_url)
        file_name, extension = file_name_with_ext.split(".")
        logger.info(
            f"[KycHandler] Downloaded PDF for resource_number: {resource_number}, file_name_with_ext: {file_name_with_ext}"
        )
        s3_path = self.upload_file_to_s3(
            file_data=file_data, extension=extension
        )
        self.create_doc(
            file_name,
            extension,
            s3_path,
            doc_type,
            doc_number=resource_number,
        )

    def fetch_pan_info_from_file(self, s3_key: str) -> PanDetail:
        logger.info(
            f"[fetch_pan_info_from_file] Fetching PAN info from file for kyc_id: {self.kyc.id}, s3_key: {s3_key}"
        )
        s3_response = self.download_file_from_s3(s3_key)
        pan_file_name_with_ext = s3_key.split("/")[-1]
        pan_file_content_type = s3_response.get("ContentType")

        if not pan_file_content_type:
            pan_file_content_type = mimetypes.guess_type(
                pan_file_name_with_ext
            )[0]

        if not pan_file_content_type:
            # fallback to jpg content type
            pan_file_content_type = MIMETYPE_JPG

        pan_detail: PanDetail = SurepassApi().fetch_pan_info_from_file(
            pan_file_name_with_ext=pan_file_name_with_ext,
            pan_file=s3_response[
                "Body"
            ],  # stream file directly without saving locally
            pan_file_content_type=pan_file_content_type,
        )
        logger.info(
            "[fetch_pan_info_from_file] PAN info fetched: %s"
            % asdict(pan_detail)
        )
        return pan_detail

    def process_upload_pan(self, s3_key: str):
        logger.info(
            f"[process_upload_pan] Processing PAN upload for kyc_id: {self.kyc.id}, s3_key: {s3_key}"
        )
        pan_detail: PanDetail = self.fetch_pan_info_from_file(s3_key)

        extension = s3_key.rsplit(".", 1)[-1].lower()
        s3_target_key = self.generate_s3_key(extension=extension)
        self.kyc.get_latest_state(state=KYCStateEnum.UPLOAD_PAN).update_data(
            data=asdict(pan_detail)
        )
        self.create_doc(
            pan_detail.pan_number,  # type: ignore
            extension,
            s3_target_key,
            DocTypeEnums.PAN_CARD,
            doc_number=pan_detail.pan_number,
        )

        # copy from tmp/companydoc to companydoc/
        # let AWS S3 lifecycle rule empty tmp/companydoc folder at regular interval
        # e.g. delete all files older than 7 days
        self.copy_file_within_s3(
            file_source=s3_key, file_destination=s3_target_key
        )

    def get_gst_data(self, kyc_state: KYCStateEnum = KYCStateEnum.VERIFICATION):
        latest_state = self.kyc.get_latest_state(state=kyc_state)
        data = latest_state.data
        return SurepassGSTParser(data).parse()

    def get_aadhaar_kyc_address(self):
        latest_state = self.kyc.get_latest_state(
            state=KYCStateEnum.VERIFICATION
        )
        data = latest_state.data
        parser = AadhaarParser()
        parser.parse(data)
        return parser.get_kyc_address()

    def get_caf_image(self):
        try:
            return self.kyc.get_doc(DocTypeEnums.CAF_IMAGE).doc_path
        except KycDocNotFoundException:
            return ""

    def get_caf_number(self) -> str:
        caf = Cafs.get_or_create_pending_caf(
            billing_account_id=self.kyc.billing_account.id,
            caf_number=Cafs.generate_caf_number(),
            resource=self.kyc.source,
        )
        logger.info(f"[get_caf_number] CAF number: {caf.caf_number}")
        return caf.caf_number

    def fill_e_sign_data(self, data):
        data["caf_number"] = self.get_caf_number()
        try:
            if self.kyc.mode == KYCModeEnum.GST.value:
                gst_data = self.get_gst_data()
                data["gst_number"] = gst_data.gstin
                data.update(
                    asdict(gst_data.get_kyc_address())
                )  # Update with GST address

            elif self.kyc.mode == KYCModeEnum.AADHAAR.value:
                try:
                    gst_data = self.get_gst_data(
                        kyc_state=KYCStateEnum.GST_INFO
                    )
                    data["gst_number"] = gst_data.gstin
                except KycStateNotFoundException:
                    logger.info(
                        f"[get_e_sign_data] GST info not found for kyc_id: {self.kyc.id}"
                    )
                data.update(
                    asdict(self.get_aadhaar_kyc_address())
                )  # Update with Aadhaar address
        except Exception as e:
            logger.error(
                f"[get_e_sign_data] Error fetching address: {e}", exc_info=True
            )

        data["image"] = self.get_caf_image()

        logger.info(f"[get_e_sign_data] E-sign data: {data}")

        return data
