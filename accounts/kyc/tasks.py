import logging

import celery

from accounts.billing_accounts.enums import DocTypeEnums
from accounts.exceptions import ExternalApiDependencyFailedException
from accounts.kyc.enums import KYCStateEnum
from accounts.kyc.exceptions import (
    InactiveGstNumberException,
    KycNotFoundException,
)
from accounts.kyc.handlers import <PERSON>yc<PERSON>and<PERSON>
from accounts.kyc.models import KYC
from accounts.kyc.parsers import (
    AadhaarFileDownloadParser,
    GstFileDownloadParser,
)
from accounts.utils.api_services.surepass import SurepassApi
from accounts.utils.api_services.surepass_helpers import get_gst_data

logger = logging.getLogger(__name__)


@celery.shared_task(bind=True)
def download_gst_pdf_task(
    self,
    gst_number: str,
    kyc_id: str,
    current_state: str,
    fail_kyc: bool = False,
):
    """Downloads GST PDF and creates a document for the KYC.

    Args:
        gst_number (str): The GST number.
        kyc_id (str): The KYC id.
        current_state (str): The current state of the KYC.
        fail_kyc (bool, optional): Whether to fail the KYC if the task fails. Defaults to False. If False, only the current state is marked as failed.

    Raises:
        KycNotFoundException: If the KYC object does not exist.
        Exception: If any error occurs.
    """
    kyc_state = KYCStateEnum.get(current_state)
    logger.info(
        f"[download_gst_pdf_task] starting task request task_id: {self.request.id}, kyc_id: {kyc_id}, gst_number: {gst_number}, kyc_state: {kyc_state}"
    )
    self.update_state(state=celery.states.STARTED)  # type: ignore
    try:
        kyc = KYC.get_object(kyc_id, current_state=kyc_state)
    except KycNotFoundException as e:
        logger.critical(
            f"[download_gst_pdf_task] KYC not found for kyc_id: {kyc_id}, error: {e}",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e

    try:
        api_data: GstFileDownloadParser = SurepassApi().download_gst_pdf(
            gst_number=gst_number
        )
        kyc_handler = KycHandler(kyc)
        kyc_handler.copy_file_from_external_source_to_s3(
            file_url=api_data.pdf_url,
            resource_number=gst_number,
            doc_type=DocTypeEnums.GST,
        )
        kyc_handler.kyc.mark_latest_state_as_completed(kyc_state)
        self.update_state(state=celery.states.SUCCESS)  # type: ignore
    except Exception as e:
        reason = str(e)
        if fail_kyc:
            kyc.mark_as_failed(
                failure_reason=kyc.generate_failure_reason(
                    reason=reason, exception_name=e.__class__.__name__
                )
            )
        else:
            kyc.mark_latest_state_as_failed(
                failure_reason=kyc.generate_failure_reason(
                    reason=reason, exception_name=e.__class__.__name__
                ),
                state=kyc_state,
            )
        logger.critical(
            "[download_gst_pdf_task] KYC GST PDF Download Failed for gst_number: %s, error: %s"
            % (gst_number, reason),
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e


@celery.shared_task(bind=True)
def download_aadhaar_pdf_task(
    self, aadhaar_number: str, aadhaar_client_id: str, kyc_id: str
):
    """Downloads Aadhaar PDF and creates a document for the KYC.

    Args:
        aadhaar_number (str): The Aadhaar number.
        aadhaar_client_id (str): The Aadhaar client id.
        kyc_id (str): The KYC id.

    Raises:
        KycNotFoundException: If the KYC object does not exist.
        BaseException: If any error occurs.
    """

    logger.info(
        "[download_aadhaar_pdf_task] starting task request task_id: %s, kyc_id: %s, aadhaar_number: %s, aadhaar_client_id: %s"
        % (
            self.request.id,
            kyc_id,
            aadhaar_number,
            aadhaar_client_id,
        )
    )
    self.update_state(state=celery.states.STARTED)  # type: ignore

    try:
        kyc = KYC.get_object(kyc_id)
    except KycNotFoundException as e:
        logger.critical(
            f"[download_aadhaar_pdf_task] KYC not found for kyc_id: {kyc_id}, error: {e}",
            title="KYC not found",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e

    try:
        api_data: (
            AadhaarFileDownloadParser
        ) = SurepassApi().download_aadhaar_pdf(
            aadhaar_client_id=aadhaar_client_id
        )
        kyc_handler = KycHandler(kyc)
        kyc_handler.copy_file_from_external_source_to_s3(
            file_url=api_data.pdf_url,
            resource_number=aadhaar_number,
            doc_type=DocTypeEnums.AADHAAR_CARD,
        )
        kyc_handler.kyc.mark_latest_state_as_completed(
            state=KYCStateEnum.VERIFICATION
        )
        self.update_state(state=celery.states.SUCCESS)  # type: ignore
    except Exception as e:
        reason = str(e)
        kyc.mark_as_failed(
            failure_reason=kyc.generate_failure_reason(
                reason, e.__class__.__name__
            )
        )
        logger.critical(
            "[download_aadhaar_pdf_task] KYC Aadhaar PDF Download Failed for aadhaar_number: %s, error: %s"
            % (aadhaar_number, reason),
            title="KYC Aadhaar PDF download failed",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e


@celery.shared_task(bind=True)
def fetch_gst_info_for_kyc_task(
    self, gst_number: str, kyc_id: str, current_state: str
):
    """Fetches GST info and updates the KYC state.

    Args:
        gst_number (str): The GST number.
        kyc_id (str): The KYC id.
        current_state(str): The KYC state name.

    Raises:
        KycNotFoundException: If the KYC object does not exist.
        Exception: If any error occurs.
        InactiveGstNumberException: If the GST number is inactive.
        ExternalApiDependencyFailedException: If the external API call fails.
    """
    kyc_state = KYCStateEnum.get(current_state)

    logger.info(
        f"[fetch_gst_info_for_kyc_task] starting task request kyc_id: {kyc_id}, gst_number: {gst_number}, kyc_state: {kyc_state}"
    )
    self.update_state(state=celery.states.STARTED)  # type: ignore
    try:
        kyc = KYC.get_object(kyc_id, current_state=kyc_state)
    except KycNotFoundException as e:
        logger.critical(
            f"[fetch_gst_info_for_kyc_task] KYC not found for kyc_id: {kyc_id}, error: {e}",
            title="KYC not found",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e

    try:
        try:
            gst_data = get_gst_data(gst_number)
        except InactiveGstNumberException as e:
            raise InactiveGstNumberException(
                "Your GST is inactive. Please use a valid GST or complete KYC via PAN."
            ) from e
        except ExternalApiDependencyFailedException as e:
            raise ExternalApiDependencyFailedException(
                "Unable to verify GST at the moment. Please try again or complete KYC using PAN."
            ) from e

        kyc.get_latest_state(state=kyc_state).update_data(gst_data.data)
        self.update_state(state=celery.states.SUCCESS)  # type: ignore

    except Exception as e:
        reason = str(e)
        logger.critical(
            f"[fetch_gst_info_for_kyc_task] KYC GST Info Fetch Failed for gst_number: {gst_number}, error: {reason}",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        kyc.mark_latest_state_as_failed(
            kyc.generate_failure_reason(reason, e.__class__.__name__)
        )
        raise e


@celery.shared_task(bind=True)
def fetch_pan_info_from_file_task(self, s3_key: str, kyc_id: str):
    logger.info(
        f"[fetch_pan_info_from_file_task] starting task request task_id: {self.request.id}, kyc_id: {kyc_id}, s3_key: {s3_key}"
    )
    self.update_state(state=celery.states.STARTED)  # type: ignore
    try:
        kyc = KYC.get_object(kyc_id)
    except KycNotFoundException as e:
        logger.critical(
            f"[fetch_pan_info_from_file_task] KYC not found for kyc_id: {kyc_id}, error: {e}",
            title="KYC not found",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        raise e
    try:
        kyc_handler = KycHandler(kyc)
        kyc_handler.process_upload_pan(s3_key)
        kyc_handler.kyc.mark_latest_state_as_completed(
            state=KYCStateEnum.UPLOAD_PAN
        )
        self.update_state(state=celery.states.SUCCESS)  # type: ignore
    except Exception as e:
        reason = str(e)
        logger.critical(
            f"[fetch_pan_info_from_file_task] KYC PAN Info Fetch Failed for kyc_id: {kyc_id}, error: {reason}",
            title="KYC PAN info fetch failed",
            exc_info=True,
        )
        self.update_state(state=celery.states.FAILURE)  # type: ignore
        kyc.mark_latest_state_as_failed(
            failure_reason=kyc.generate_failure_reason(
                reason, e.__class__.__name__
            ),
            state=KYCStateEnum.UPLOAD_PAN,
        )
        raise e
