from rest_framework import status

from accounts.exceptions import (
    BaseExceptionV2,
    ExternalApiDependencyFailedException,
    S3ObjectNotFoundException,
)


class AadhaarException(BaseExceptionV2):
    http_status_code = status.HTTP_424_FAILED_DEPENDENCY
    error_code = status.HTTP_424_FAILED_DEPENDENCY


class AadhaarParserException(AadhaarException):
    pass


class AadhaarSendOtpApiException(AadhaarException):
    pass


class AadhaarVerifyOtpApiException(AadhaarException):
    pass


class InvalidAadhaarNumberException(AadhaarException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = (
        "Invalid Aadhar format. Please enter a valid 12-digit Aadhaar number."
    )
    error_code = status.HTTP_400_BAD_REQUEST
    errors = "Invalid Aadhar format."


class InvalidAadhaarNumberOtpException(AadhaarException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = (
        "Invalid Aadhaar number OTP. Please enter a valid Aadhaar number OTP."
    )
    error_code = status.HTTP_400_BAD_REQUEST
    errors = "Invalid Aadhaar number OTP."


class InvalidAadhaarClientIdException(AadhaarException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "Invalid Aadhaar client id."
    error_code = status.HTTP_400_BAD_REQUEST
    errors = "Invalid Aadhaar client id."


class InvalidGstFormatException(BaseExceptionV2):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "Invalid GSTIN format. Please enter a valid 15-digit GST number."
    error_code = status.HTTP_400_BAD_REQUEST
    errors = "Invalid GST number."


class InactiveGstNumberException(BaseExceptionV2):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "Invalid or inactive GST number. Please check and try again"
    error_code = status.HTTP_400_BAD_REQUEST


class NonOTPAttemptExceeded(BaseExceptionV2):
    http_status_code = status.HTTP_429_TOO_MANY_REQUESTS
    message = "Maximum attempts exceeded. Please try again after some time."
    error_code = status.HTTP_429_TOO_MANY_REQUESTS


class OtpDataNotFoundException(BaseExceptionV2):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "OTP expired. Please request a new OTP."
    error_code = status.HTTP_400_BAD_REQUEST
    errors = "OTP data not found or expired."


class InvalidOtpException(BaseExceptionV2):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "Incorrect OTP. Please try again."
    error_code = status.HTTP_400_BAD_REQUEST
    errors = "Input OTP does not match."


class KycFailedException(BaseExceptionV2):
    pass


class KycStateNotFoundException(BaseExceptionV2):
    pass


class ExistingKycException(BaseExceptionV2):
    http_status_code = status.HTTP_409_CONFLICT
    message = "KYC is already in progress or completed"
    error_code = status.HTTP_409_CONFLICT


class KycNotFoundException(BaseExceptionV2):
    http_status_code = status.HTTP_404_NOT_FOUND
    message = "KYC not found."
    error_code = status.HTTP_404_NOT_FOUND


class KycFileDownloadException(ExternalApiDependencyFailedException):
    pass


class KycFileDownloadParserException(ExternalApiDependencyFailedException):
    pass


class VideoKycException(ExternalApiDependencyFailedException):
    pass


class TransitionNotAllowedException(BaseExceptionV2):
    http_status_code = status.HTTP_412_PRECONDITION_FAILED
    message = "Not allowed to transition from current state."
    error_code = status.HTTP_412_PRECONDITION_FAILED


class PanException(BaseExceptionV2):
    http_status_code = status.HTTP_424_FAILED_DEPENDENCY
    error_code = status.HTTP_424_FAILED_DEPENDENCY


class PanOCRParserException(PanException):
    pass


class PanUploadAttemptsLimitExceededException(PanException):
    http_status_code = status.HTTP_429_TOO_MANY_REQUESTS
    message = "PAN upload attempts limit exceeded."
    error_code = status.HTTP_429_TOO_MANY_REQUESTS


class InvalidPanUploadException(PanException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "Invalid PAN upload request."
    error_code = status.HTTP_400_BAD_REQUEST


class PanNotFoundException(S3ObjectNotFoundException):
    message = "PAN object not found."
    errors = "PAN object not found."


class PanOcrApiException(PanException):
    pass
