import typing as t
from dataclasses import dataclass


@dataclass
class KYCAddress:
    address: str = ""
    area: str = ""
    city: str = ""
    state: str = ""
    pin_code: str = ""


@dataclass
class AadhaarSendOtpApiResponseData:
    client_id: str = ""
    otp_sent: bool = False
    if_number: bool = False
    valid_aadhaar: bool = False
    status: str = ""

    def is_successful(self) -> bool:
        return (
            bool(self.client_id)
            and self.otp_sent
            and self.if_number
            and self.valid_aadhaar
            and bool(self.status)
        )

    def get_failure_reason(self) -> str:
        if not self.valid_aadhaar:
            return "Invalid Aadhaar number."
        if not self.if_number:
            return "Aadhaar number does not have a linked mobile number."
        if not self.client_id:
            return "Missing client_id in API response."
        if not self.otp_sent:
            return "Got otp_sent as false in API response."
        if not self.status:
            return "Missing status field in API response."
        return "External API dependency failed."


@dataclass
class AadhaarAddress:
    country: str = ""
    dist: str = ""
    state: str = ""
    po: str = ""
    loc: str = ""
    vtc: str = ""
    subdist: str = ""
    street: str = ""
    house: str = ""
    landmark: str = ""


@dataclass
class AadhaarDetail:
    client_id: str = ""
    full_name: str = ""
    aadhaar_number: str = ""
    dob: str = ""
    gender: str = ""
    address: AadhaarAddress = AadhaarAddress()
    face_status: bool = False
    face_score: float = -1
    zip: str = ""
    profile_image: str = ""
    has_image: bool = False
    email_hash: str = ""
    mobile_hash: str = ""
    raw_xml: str = ""
    zip_data: str = ""
    care_of: str = ""
    share_code: str = ""
    mobile_verified: bool = False
    reference_id: str = ""
    aadhaar_pdf: t.Optional[str] = None
    status: str = ""
    uniqueness_id: str = ""


@dataclass
class PanDetail:
    client_id: str = ""
    pan_number: t.Optional[str] = ""
    full_name: t.Optional[str] = ""
    father_name: t.Optional[str] = ""
    dob: t.Optional[str] = ""
