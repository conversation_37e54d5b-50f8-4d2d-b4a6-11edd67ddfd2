import json
import logging
import typing as t
import uuid

from django.db import models, transaction
from django.utils import timezone

from accounts.billing_accounts.models import BillingAccounts
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCSourceEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.exceptions import (
    KycNotFoundException,
    KycStateNotFoundException,
)
from accounts.utils.common import get_trace_id

logger = logging.getLogger(__name__)


class KYC(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False
    )
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    current_state = models.CharField(
        max_length=30,
        choices=KYCStateEnum.choices(),
        default=KYCStateEnum.VERIFICATION.value,
    )
    mode = models.CharField(
        max_length=20,
        choices=KYCModeEnum.choices(),
    )
    source = models.CharField(
        max_length=20,
        choices=KYCSourceEnum.choices(),
        default=KYCSourceEnum.MYOPERATOR.value,
    )
    status = models.SmallIntegerField(
        choices=KYCStatusEnum.choices(), default=KYCStatusEnum.PENDING.value
    )
    expiry = models.DateTimeField(default=None, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "kycs"

    @classmethod
    @transaction.atomic
    def initialize_kyc(
        cls,
        mode: KYCModeEnum,
        billing_account: BillingAccounts,
        data: t.Dict,
        source: KYCSourceEnum = KYCSourceEnum.MYOPERATOR,
    ) -> "KYC":
        """Initializes a new KYC along with its initial state.

        Args:
            mode (KYCModeEnum): The mode of KYC.
            billing_account (BillingAccounts): The billing account.
            data (t.Dict): The data to be stored in the state.
            source (KYCSourceEnum, optional): The source of the KYC. Defaults to KYCSourceEnum.MYOPERATOR.

        Returns:
            KYC: The created KYC.
        """

        logger.info(
            f"[KYC] Initializing KYC for billing account (id: {billing_account.id}) with mode: {mode.value}, source: {source.value}"
        )
        obj = cls.objects.create(
            billing_account=billing_account,
            mode=mode.value,
            status=KYCStatusEnum.PENDING.value,
            current_state=KYCStateEnum.VERIFICATION.value,
            source=source.value,
        )
        obj.create_kyc_state(data=data)
        return obj

    def create_kyc_state(
        self,
        data: t.Dict,
        task_id: t.Optional[str] = None,
        status: KYCStateStatusEnum = KYCStateStatusEnum.PENDING,
    ) -> "KYCState":
        """Creates a new KYC state.

        Args:
            data (t.Dict): The data to be stored in the state.
            task_id (t.Optional[str], optional): The background task id. Defaults to None.
            status (KYCStateStatusEnum, optional): The status of the state. Defaults to KYCStateStatusEnum.PENDING.

        Returns:
            KYCState: The created KYC state.
        """

        logger.info(
            f"[KYC] Creating KYC state with state: {self.current_state}, status: {status.value} for KYC (id: {self.id})"
        )
        return self.states.create(
            state=self.current_state,
            status=status.value,
            task_id=task_id,
            data=data,
        )

    @classmethod
    def get_object(
        cls, id: str, current_state: t.Optional[KYCStateEnum] = None
    ) -> "KYC":
        """Fetches the KYC object from the database.

        Args:
            id (str): The KYC id.

        Raises:
            KycNotFoundException: If the KYC object does not exist.

        Returns:
            KYC: The KYC object.
        """

        try:
            if current_state:
                return cls.objects.get(id=id, current_state=current_state.value)
            return cls.objects.get(id=id)
        except cls.DoesNotExist:
            raise KycNotFoundException(f"KYC (id: {id}) not found")

    def get_latest_state(
        self, state: t.Optional[KYCStateEnum] = None
    ) -> "KYCState":
        """Fetches the latest KYC state. Also, filters by state if provided.

        Args:
            state (KYCStateEnum, optional): The state to filter by. Defaults to None.

        Raises:
            KycStateNotFoundException: If no KYC state found.

        Returns:
            KYCState: The latest KYC state.
        """

        kyc_states = self.states.all()

        if state and isinstance(state, KYCStateEnum):
            kyc_states = kyc_states.filter(state=state.value)

        latest_kyc_state = kyc_states.order_by("-started_at").first()
        if not latest_kyc_state:
            error_msg = f"KYC state not found for KYC (id: {self.id})"
            logger.critical(
                f"[KYC] {error_msg}",
                title="KYC state not found",
                exc_info=True,
            )
            raise KycStateNotFoundException(error_msg)
        return latest_kyc_state

    def is_marked_as_failed(self) -> bool:
        """Checks if the KYC is marked as failed.

        Returns:
            bool: True if the KYC is marked as failed, False otherwise.
        """

        return self.status == KYCStatusEnum.FAILED.value

    def generate_failure_reason(
        self, reason: str, exception_name: t.Optional[str] = None
    ):
        failure_reason = json.dumps(
            {
                "reason": reason,
                "trace_id": get_trace_id(),
                "exception": (
                    exception_name if exception_name else Exception.__name__
                ),
            }
        )
        return failure_reason

    @transaction.atomic
    def mark_as_failed(self, failure_reason: str):
        """Marks the KYC as failed and updates the latest KYC state as failed with the provided reason.

        Args:
            failure_reason (str): The reason for marking the KYC as failed.
        """

        logger.info(
            "[KYC] Marking KYC (id: %s) as failed with reason: %s"
            % (self.id, failure_reason),
            title="KYC mark failed",
        )
        self.status = KYCStatusEnum.FAILED.value
        self.save()

        self.mark_latest_state_as_failed(failure_reason=failure_reason)

    def mark_latest_state_as_failed(
        self, failure_reason: str, state: t.Optional[KYCStateEnum] = None
    ):
        """Marks the latest KYC state as failed with the provided reason.

        Args:
            failure_reason (str): The reason for marking the KYC state as failed.
            state (KYCStateEnum, optional): The state to filter by. Defaults to None.
        """

        latest_kyc_state = self.get_latest_state(state=state)

        logger.info(
            "[KYC] Marking KYC state (id: %s) as failed with reason: %s"
            % (latest_kyc_state.id, failure_reason),
            title="KYC state mark failed",
        )
        latest_kyc_state.status = KYCStateStatusEnum.FAILED.value
        latest_kyc_state.failure_reason = failure_reason
        latest_kyc_state.save()

    @transaction.atomic
    def mark_as_completed(self):
        """Marks the KYC as completed and updates the latest KYC state as completed."""

        logger.info("[KYC] Marking KYC (id: %s) as completed" % self.id)
        self.status = KYCStatusEnum.COMPLETED.value
        self.mark_latest_state_as_completed()
        self.save()

    def mark_latest_state_as_completed(
        self, state: t.Optional[KYCStateEnum] = None
    ) -> "KYCState":
        """Marks the latest KYC state as completed.

        Args:
            state (t.Optional[KYCStateEnum], optional): The state to filter by. Defaults to None.

        Returns:
            KYCState: The latest KYC state.
        """

        latest_kyc_state = self.get_latest_state(state=state)
        logger.info(
            f"[KYC] Marking KYC state (id: {latest_kyc_state.id}) as completed"
        )
        latest_kyc_state.status = KYCStateStatusEnum.COMPLETED.value
        latest_kyc_state.completed_at = timezone.now()
        latest_kyc_state.save()

        return latest_kyc_state


class KYCState(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False
    )
    kyc = models.ForeignKey(
        KYC, on_delete=models.CASCADE, related_name="states"
    )
    state = models.CharField(max_length=30, choices=KYCStateEnum.choices())
    status = models.SmallIntegerField(
        choices=KYCStateStatusEnum.choices(),
        default=KYCStateStatusEnum.PENDING.value,
    )
    data = models.JSONField(default=dict)
    task_id = models.CharField(max_length=36, default=None, null=True)
    failure_reason = models.CharField(max_length=255, default=None, null=True)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(default=None, null=True)

    objects = models.Manager()

    class Meta:
        db_table = "kyc_states"

    def update_task_id(self, task_id: str):
        self.task_id = task_id
        self.save()

    def update_data(self, data: t.Dict):
        self.data = data
        self.save()

    def is_completed(self) -> bool:
        return self.status == KYCStateStatusEnum.COMPLETED.value
