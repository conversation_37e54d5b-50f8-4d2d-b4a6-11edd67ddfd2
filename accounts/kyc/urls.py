from django.urls import path

from accounts.kyc.views.aadhaar import AadhaarSendOtpView, AadhaarVerifyOtpView
from accounts.kyc.views.digilocker import DigilockerInitView
from accounts.kyc.views.e_sign import ESignView
from accounts.kyc.views.gst import (
    GstNonOtpView,
    GstSendOtpView,
    GstVerifyOtpView,
)
from accounts.kyc.views.pan import PanUploadView
from accounts.kyc.views.video_kyc import VideoKycInitView

app_name = "kyc"

urlpatterns = [
    path(
        "<str:kyc_id>/gst/non-otp",
        view=GstNonOtpView.as_view(),
        name="gst_non_otp",
    ),
    path(
        "<str:kyc_id>/human-verification/init",
        view=VideoKycInitView.as_view(),
        name="video_kyc_init",
    ),
    path(
        "<str:kyc_id>/e-sign",
        view=ESignView.as_view(),
        name="e_sign_init",
    ),
    path("gst/send-otp", view=GstSendOtpView.as_view(), name="gst_send_otp"),
    path(
        "gst/verify-otp", view=GstVerifyOtpView.as_view(), name="gst_verify_otp"
    ),
    path(
        "aadhaar/send-otp",
        view=AadhaarSendOtpView.as_view(),
        name="aadhaar-send-otp",
    ),
    path(
        "aadhaar/verify-otp",
        view=AadhaarVerifyOtpView.as_view(),
        name="aadhaar-verify-otp",
    ),
    path(
        "<str:kyc_id>/pan/upload",
        view=PanUploadView.as_view(),
        name="pan-upload",
    ),
    path(
        "<str:kyc_id>/digilocker/init",
        view=DigilockerInitView.as_view(),
        name="digilocker-init",
    ),
]
