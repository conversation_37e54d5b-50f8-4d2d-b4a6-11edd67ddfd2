from typing import OrderedDict

from rest_framework.serializers import ModelSerializer, SerializerMethodField

from accounts.kyc.enums import (
    KYCModeEnum,
    KYCSourceEnum,
    KYCStateEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC
from accounts.kyc.sm.statemachines import KYCStateMachine


class KYCSerializer(ModelSerializer):
    KYC_STATE_FIELD = "kyc_states"

    current_state = SerializerMethodField()
    mode = SerializerMethodField()
    source = SerializerMethodField()
    status = SerializerMethodField()

    def get_current_state(self, obj: KYC):
        return KYCStateEnum.get_name(obj.current_state).lower()

    def get_mode(self, obj: KYC):
        return KYCModeEnum.get_name(obj.mode).lower()

    def get_source(self, obj: KYC):
        return KYCSourceEnum.get_name(obj.source).lower()

    def get_status(self, obj: KYC):
        return KYCStatusEnum.get_name(obj.status).lower()

    class Meta:
        model = KYC
        fields = [
            "id",
            "billing_account_id",
            "current_state",
            "mode",
            "source",
            "status",
            "expiry",
            "created",
            "modified",
        ]
        extra_kwargs = {
            "id": {"read_only": True},
            "created": {"read_only": True},
            "modified": {"read_only": True},
        }

    def set_state_mapping(self, repr: OrderedDict):
        expand = self.context.get(
            "expand", []
        )  # expand is a list of fields to expand

        if self.KYC_STATE_FIELD in expand:
            repr[self.KYC_STATE_FIELD] = KYCStateMachine().get_states_mapping()

        return repr

    def to_representation(self, instance):
        repr = super().to_representation(instance)
        repr = self.set_state_mapping(repr)
        return repr
