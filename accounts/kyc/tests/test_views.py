from django.urls import reverse

import pytest
from rest_framework import status

from accounts.kyc.cache_handler import GstCacheHandler
from accounts.kyc.enums import KYCStatusEnum
from accounts.kyc.exceptions import ExistingKycException
from accounts.kyc.models import KYC
from accounts.kyc.validations import validate_existing_kyc


@pytest.fixture
def url():
    return reverse("kyc:gst_send_otp")


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "api_url",
    [
        reverse("kyc:gst_send_otp"),
        reverse("kyc:gst_verify_otp"),
    ],
)
def test_gst_otp_apis_require_valid_billing_account_id_in_header(
    client,
    api_url,
    valid_gst_number,
):
    response = client.post(
        api_url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {
        "status": "error",
        "code": "permission_denied",
        "message": "You do not have permission to perform this action.",
        "errors": {"detail": "Invalid billing_account_id in headers!"},
    }


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "kyc_status",
    [KYCStatusEnum.EXPIRED.value, KYCStatusEnum.FAILED.value, None],
)
def test_gst_send_otp_success(
    client,
    url,
    valid_gst_number,
    mock_gst_detail_api_with_custom_data,
    mock_smsg_api,
    api_headers,
    billing_account,
    kyc_status,
):
    mock_gst_detail_api_with_custom_data(
        mobile_number="**********", gst_number=valid_gst_number
    )
    mock_smsg_api()

    if kyc_status:
        KYC.objects.create(billing_account=billing_account, status=kyc_status)

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "status": "success",
        "code": 200,
        "message": "OTP sent successfully on the phone number linked to GST number.",
        "data": {"masked_phone_number": "*******210"},
    }

    handler = GstCacheHandler(valid_gst_number)
    data = handler.get_otp_data()
    assert data["phone_number"] == "**********"
    assert "otp" in data
    assert data["verification_attempts"] == "0"


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "gst_number,expected_message",
    [
        (
            "invalid_gst",
            "Invalid GSTIN format. Please enter a valid 15-digit GST number.",
        ),
        ("", "Invalid GSTIN format. Please enter a valid 15-digit GST number."),
        (
            None,
            "Invalid GSTIN format. Please enter a valid 15-digit GST number.",
        ),
    ],
)
def test_gst_send_otp_invalid_gst_number(
    client, url, gst_number, expected_message, api_headers
):
    response = client.post(
        url,
        {"gst_number": gst_number} if gst_number is not None else {},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "code": status.HTTP_400_BAD_REQUEST,
        "errors": {"detail": "Invalid GST number."},
        "message": expected_message,
        "status": "error",
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_send_otp_invalid_gst_number_from_api(
    client,
    url,
    valid_gst_number,
    mock_gst_detail_api_with_custom_data,
    api_headers,
):
    mock_gst_detail_api_with_custom_data(
        expected_res={"error": "Invalid GSTIN number"},
        status_code=status.HTTP_400_BAD_REQUEST,
    )

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_424_FAILED_DEPENDENCY
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_424_FAILED_DEPENDENCY,
        "message": "GST details fetch failed. Please try again later.",
        "errors": {"detail": "Invalid GSTIN number."},
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_send_otp_max_attempts_exceeded(
    client,
    url,
    valid_gst_number,
    mock_gst_detail_api_with_custom_data,
    api_headers,
):
    mock_gst_detail_api_with_custom_data(
        mobile_number="**********", gst_number=valid_gst_number
    )

    cache_handler = GstCacheHandler(gst_number=valid_gst_number)
    for _ in range(5):
        cache_handler.incr_send_attempt()

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_429_TOO_MANY_REQUESTS,
        "message": "Send OTP limit exceeded. Maximum allowed: 5",
        "errors": {"detail": "Send OTP limit exceeded. Maximum allowed: 5"},
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_send_otp_smsg_failed(
    client,
    url,
    valid_gst_number,
    mock_gst_detail_api_with_custom_data,
    mock_smsg_api,
    api_headers,
):
    mock_gst_detail_api_with_custom_data(
        mobile_number="**********", gst_number=valid_gst_number
    )
    mock_smsg_api(
        expected_response={"success": False, "message": "Rate limit exceeded"},
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
    )

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_424_FAILED_DEPENDENCY
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_424_FAILED_DEPENDENCY,
        "message": "Failed to send OTP",
        "errors": {"detail": "Failed to send OTP"},
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_send_otp_inactive_gst_number(
    client,
    url,
    valid_gst_number,
    mock_gst_detail_api_with_custom_data,
    api_headers,
):
    mock_gst_detail_api_with_custom_data(
        gst_number=valid_gst_number, gst_status="inactive"
    )

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_400_BAD_REQUEST,
        "message": "Invalid or inactive GST number. Please check and try again",
        "errors": {
            "detail": "Invalid or inactive GST number. Please check and try again"
        },
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_validate_existing_kyc_no_kyc(billing_account):
    """Test validate_existing_kyc when no KYC exists."""

    # Should not raise any exception
    validate_existing_kyc(billing_account)


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "kyc_status",
    [
        KYCStatusEnum.EXPIRED.value,
        KYCStatusEnum.FAILED.value,
    ],
)
def test_validate_existing_kyc_with_expired_or_failed_kyc(
    billing_account, kyc_status
):
    """Test validate_existing_kyc with expired KYC."""

    # Create an expired KYC
    KYC.objects.create(billing_account=billing_account, status=kyc_status)

    # Should not raise any exception since expired KYCs are excluded
    validate_existing_kyc(billing_account)


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "kyc_status",
    [
        KYCStatusEnum.PENDING.value,
        KYCStatusEnum.COMPLETED.value,
    ],
)
def test_validate_existing_kyc_with_active_kyc(billing_account, kyc_status):
    """Test validate_existing_kyc with active KYC."""
    # Create an active KYC
    KYC.objects.create(billing_account=billing_account, status=kyc_status)

    # Should raise ExistingKycException
    with pytest.raises(ExistingKycException):
        validate_existing_kyc(billing_account)


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "kyc_status",
    [
        KYCStatusEnum.PENDING.value,
        KYCStatusEnum.COMPLETED.value,
    ],
)
def test_gst_send_otp_with_active_kyc(
    client,
    url,
    valid_gst_number,
    api_headers,
    billing_account,
    kyc_status,
):
    """Test that send OTP endpoint fails when there is an existing active KYC."""
    # Create an active KYC
    KYC.objects.create(billing_account=billing_account, status=kyc_status)

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )

    assert response.status_code == status.HTTP_409_CONFLICT
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_409_CONFLICT,
        "message": "KYC is already in progress or completed",
        "errors": {"detail": "KYC is already in progress or completed"},
    }
