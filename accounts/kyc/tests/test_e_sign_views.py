from django.urls import reverse

import pytest
from rest_framework import status

from accounts.cafs.enums import CafStatusEnum
from accounts.cafs.tests.factories import CafFactory
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)


@pytest.fixture
def e_sign_url():
    yield reverse("kyc:e_sign_init", kwargs={"kyc_id": "test_kyc_id"})


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "verification_state",
    [
        KYCStateEnum.GST_INFO,
        KYCStateEnum.UPLOAD_PAN,
        KYCStateEnum.DIGILOCKER_PAN,
    ],
)
def test_e_sign_get_data_for_aadhaar_kyc(
    e_sign_url,
    client,
    api_headers,
    create_kyc_for_state,
    load_json,
    caf_image,
    valid_aadhaar_number,
    verification_state,
):
    data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_200_success_response.json"
    )["data"]

    gst_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"]
    kyc = create_kyc_for_state(
        KYCModeEnum.AADHAAR,
        verification_state,
        kyc=create_kyc_for_state(
            KYCModeEnum.AADHAAR, KYCStateEnum.VERIFICATION, state_data=data
        ),
        state_data=(
            gst_data if verification_state == KYCStateEnum.GST_INFO else {}
        ),
    )
    caf_image_obj = caf_image(kyc, valid_aadhaar_number)

    kyc = create_kyc_for_state(
        KYCModeEnum.AADHAAR,
        KYCStateEnum.VIDEO_KYC,
        kyc=kyc,
        state_data={"image": caf_image_obj.doc_path},
    )

    url = e_sign_url.replace("test_kyc_id", str(kyc.id))
    response = client.get(
        url,
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()

    assert json_response["status"] == "success"
    assert json_response["code"] == status.HTTP_200_OK
    assert json_response["message"] == "E-sign initialized successfully."
    assert json_response["data"]["caf_number"] is not None
    assert (
        json_response["data"]["address"]
        == "house_india street_india landmark_india"
    )
    assert json_response["data"]["area"] == "loc_india"
    assert json_response["data"]["city"] == "dist_india"
    assert json_response["data"]["state"] == "state_india"
    assert json_response["data"]["pin_code"] == "110001"
    assert (
        json_response["data"]["gst_number"] == gst_data["gstin"]
        if verification_state == KYCStateEnum.GST_INFO
        else json_response["data"]["gst_number"] == ""
    )
    assert json_response["data"]["image"] == caf_image_obj.doc_path

    assert kyc.mode == KYCModeEnum.AADHAAR.value
    assert kyc.current_state == KYCStateEnum.VIDEO_KYC.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VIDEO_KYC.value
    assert latest_state.status == KYCStateStatusEnum.COMPLETED.value
    assert latest_state.data == {"image": caf_image_obj.doc_path}
    assert latest_state.failure_reason is None


@pytest.mark.unittest
@pytest.mark.django_db
@pytest.mark.parametrize(
    "existing_caf",
    [
        True,
        False,
    ],
)
def test_e_sign_get_data_for_gst_kyc(
    e_sign_url,
    client,
    api_headers,
    create_kyc_for_state,
    load_json,
    caf_image,
    valid_aadhaar_number,
    existing_caf,
):
    gst_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"]
    kyc = create_kyc_for_state(
        KYCModeEnum.GST, KYCStateEnum.VERIFICATION, state_data=gst_data
    )
    caf_image_obj = caf_image(kyc, valid_aadhaar_number)
    kyc = create_kyc_for_state(
        KYCModeEnum.GST,
        KYCStateEnum.VIDEO_KYC,
        kyc=kyc,
        state_data={"image": caf_image_obj.doc_path},
    )
    caf = None
    if existing_caf:
        caf = CafFactory(
            billing_account_id=kyc.billing_account_id,
            status=CafStatusEnum.PENDING.value,
            ekyc_verifier="",
            resource=kyc.source,
        )
    url = e_sign_url.replace("test_kyc_id", str(kyc.id))
    response = client.get(
        url,
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()

    assert json_response["status"] == "success"
    assert json_response["code"] == status.HTTP_200_OK
    assert json_response["message"] == "E-sign initialized successfully."
    if existing_caf:
        assert json_response["data"]["caf_number"] == caf.caf_number
    else:
        assert json_response["data"]["caf_number"] is not None
    assert (
        json_response["data"]["address"]
        == "Building 10A 5 TTC Industrial Area Reliance Corporate Park Thane Belapur Road"
    )
    assert json_response["data"]["area"] == "Ghansoli, Navi Mumbai"
    assert json_response["data"]["city"] == "Thane"
    assert json_response["data"]["state"] == "Maharashtra"
    assert json_response["data"]["pin_code"] == "400701"
    assert json_response["data"]["gst_number"] == gst_data["gstin"]
    assert json_response["data"]["image"] == caf_image_obj.doc_path

    assert kyc.mode == KYCModeEnum.GST.value
    assert kyc.current_state == KYCStateEnum.VIDEO_KYC.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VIDEO_KYC.value
    assert latest_state.status == KYCStateStatusEnum.COMPLETED.value
    assert latest_state.data == {"image": caf_image_obj.doc_path}
    assert latest_state.failure_reason is None


@pytest.mark.unittest
@pytest.mark.django_db
def test_e_sign_get_data_kyc_not_found(e_sign_url, client, api_headers):
    url = e_sign_url.replace("test_kyc_id", str("12345"))
    response = client.get(
        url,
        content_type="application/json",
        **api_headers,
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_404_NOT_FOUND,
        "message": "KYC not found.",
        "errors": {"detail": "KYC not found."},
    }


@pytest.mark.unittest
@pytest.mark.django_db
def test_e_sign_get_data_kyc_default_data(
    e_sign_url, load_json, client, api_headers, create_kyc_for_state
):
    kyc = create_kyc_for_state(
        KYCModeEnum.GST, KYCStateEnum.VERIFICATION, state_data={}
    )

    url = e_sign_url.replace("test_kyc_id", str(kyc.id))
    response = client.get(
        url,
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()

    assert json_response["status"] == "success"
    assert json_response["code"] == status.HTTP_200_OK
    assert json_response["message"] == "E-sign initialized successfully."
    assert json_response["data"]["caf_number"] is not None
    assert json_response["data"]["address"] == ""
    assert json_response["data"]["area"] == ""
    assert json_response["data"]["city"] == ""
    assert json_response["data"]["state"] == ""
    assert json_response["data"]["pin_code"] == ""
    assert json_response["data"]["gst_number"] == ""
    assert json_response["data"]["image"] == ""

    assert kyc.mode == KYCModeEnum.GST.value
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.COMPLETED.value
    assert latest_state.data == {}
    assert latest_state.failure_reason is None
