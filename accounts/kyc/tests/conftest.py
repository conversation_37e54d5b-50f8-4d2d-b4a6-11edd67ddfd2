import typing as t
from typing import Any, Callable, Dict, Generator, Optional

from django.conf import settings

import pytest
from rest_framework import status

from accounts.billing_accounts.enums import DocTypeEnums
from accounts.billing_accounts.models import BillingAccountDocs, BillingAccounts
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.constants import PAN_CONFIG
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC
from accounts.kyc.tests.factories import KYCFactory
from accounts.tests.factories.billing_accounts import DocTypeFactory


@pytest.fixture
def billing_account() -> Any:
    """Create a billing account fixture."""
    if BillingAccounts.objects.count() == 0:
        return BillingAccountFactory()
    else:
        return BillingAccounts.objects.first()


@pytest.fixture
def gst_doc_type() -> Any:
    """Create GST document type fixture."""
    return DocTypeFactory(
        short_code=DocTypeEnums.GST.value,
        name=DocTypeEnums.GST.name,
    )


@pytest.fixture
def get_caf_image_doc_type():
    return DocTypeFactory(
        short_code=DocTypeEnums.CAF_IMAGE.value,
        name=DocTypeEnums.CAF_IMAGE.name,
    )


@pytest.fixture
def aadhaar_doc_type() -> Any:
    """Create Aadhaar document type fixture."""
    return DocTypeFactory(
        short_code=DocTypeEnums.AADHAAR_CARD.value,
        name=DocTypeEnums.AADHAAR_CARD.name,
    )


@pytest.fixture()
def caf_image(
    get_caf_image_doc_type,
):
    def wrap(kyc: KYC, doc_number: str) -> BillingAccountDocs:
        billing_account = kyc.billing_account
        return BillingAccountDocs.create_doc(
            billing_account,
            kyc,
            "https://s3.aws.com/user.jpg",
            DocTypeEnums.CAF_IMAGE,
            "caf_image",
            "jpg",
            doc_number,
        )

    yield wrap


@pytest.fixture
def pan_doc_type() -> Any:
    """Create PAN document type fixture."""
    return DocTypeFactory(
        short_code=DocTypeEnums.PAN_CARD.value,
        name=DocTypeEnums.PAN_CARD.name,
    )


@pytest.fixture
def mock_gst_detail_api_with_custom_data(load_json, mock_gst_detail_api):
    def wrap(
        mobile_number: Optional[str] = None,
        gst_number: Optional[str] = None,
        gst_status: Optional[str] = None,
        expected_res: Optional[Dict[str, Any]] = None,
        status_code: int = status.HTTP_200_OK,
    ):
        if not expected_res:
            expected_res = load_json(
                "accounts/kyc/tests/fixtures/gst_details.json"
            )

        if mobile_number and expected_res:
            expected_res["data"]["contact_details"]["principal"][
                "mobile"
            ] = mobile_number
        if gst_number and expected_res:
            expected_res["data"]["gstin"] = gst_number
        if gst_status and expected_res:
            expected_res["data"]["gstin_status"] = gst_status

        res = mock_gst_detail_api(expected_res, status_code)
        return res

    return wrap


@pytest.fixture
def api_headers(billing_account):
    return {
        f"HTTP_{settings.BILLING_ACCOUNT_HEADER_NAME}": billing_account.id,
    }


@pytest.fixture
def valid_gst_number(load_json):
    return load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"][
        "gstin"
    ]


@pytest.fixture
def valid_aadhaar_number():
    return "************"


@pytest.fixture
def valid_aadhaar_client_id():
    return "aadhaar_v2_MyTbbbjfikBvczitbacn"


@pytest.fixture
def valid_aadhaar_otp():
    return "123456"


@pytest.fixture
def create_kyc_for_state(billing_account):
    def wrap(
        kyc_mode: KYCModeEnum,
        kyc_state: KYCStateEnum,
        kyc_status: KYCStatusEnum = KYCStatusEnum.PENDING,
        state_status: KYCStateStatusEnum = KYCStateStatusEnum.COMPLETED,
        kyc: t.Optional[KYC] = None,
        state_failure_reason: str = "Default error.",
        state_data: t.Dict = {},
    ) -> KYC:
        """Create a KYC object for a given state.

        Args:
            kyc_mode (KYCModeEnum): The KYC mode.
            kyc_state (KYCStateEnum): The KYC state.
            kyc_status (KYCStatusEnum, optional): The KYC status. Defaults to KYCStatusEnum.PENDING.
            state_status (KYCStateStatusEnum, optional): The state status. Defaults to KYCStateStatusEnum.COMPLETED.
            kyc (t.Optional[KYC], optional): The KYC object. Defaults to None.
            state_failure_reason (str, optional): The state failure reason. Defaults to "Default error.".
            state_data (t.Dict, optional): The state data. Defaults to {}.

        Returns:
            KYC: The KYC object.
        """

        if not kyc:
            kyc = KYCFactory(
                billing_account=billing_account,
                current_state=kyc_state.value,
                mode=kyc_mode.value,
                status=kyc_status.value,
            )
        else:
            kyc.current_state = kyc_state.value
            kyc.status = kyc_status.value
            kyc.save()

        kyc.create_kyc_state(data=state_data)
        if state_status == KYCStateStatusEnum.COMPLETED:
            kyc.mark_latest_state_as_completed(state=kyc_state)
        elif state_status == KYCStateStatusEnum.FAILED:
            kyc.mark_latest_state_as_failed(
                failure_reason=state_failure_reason, state=kyc_state
            )

        return kyc

    return wrap


@pytest.fixture
def valid_pan_number():
    return "**********"


@pytest.fixture
def valid_pan_file_in_s3_tmp():
    def wrap(extension: str):
        return f"{PAN_CONFIG['S3_TMP_PREFIX']}pan_file{extension}"

    return wrap
