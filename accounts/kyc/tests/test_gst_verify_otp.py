from django.conf import settings
from django.urls import reverse

import pytest
from rest_framework import status

from accounts.billing_accounts.models import BillingAccountDocs, BillingAccounts
from accounts.kyc.cache_handler import GstCacheHandler
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCSourceEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC


@pytest.fixture
def url():
    return reverse("kyc:gst_verify_otp")


@pytest.fixture
def valid_otp():
    return "123456"


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "kyc_status",
    [KYCStatusEnum.FAILED.value, KYCStatusEnum.EXPIRED.value, None],
)
def test_gst_verify_otp_success(
    client,
    url,
    valid_gst_number,
    valid_otp,
    mock_gst_detail_api_with_custom_data,
    api_headers,
    mock_surepass_api_gst_pdf,
    mock_s3_bucket_download,
    mock_s3_bucket,
    load_json,
    gst_doc_type,
    kyc_status,
    billing_account,
):
    if kyc_status:
        KYC.objects.create(billing_account=billing_account, status=kyc_status)

    # Setup cache with OTP data
    cache_handler = GstCacheHandler(gst_number=valid_gst_number)
    phone_number = "**********"
    cache_handler.set_otp_data(
        phone_number, valid_otp, verification_attempts="0"
    )
    gst_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")
    # Setup API response
    mock_gst_detail_api_with_custom_data(
        mobile_number=phone_number,
        gst_number=valid_gst_number,
        expected_res=gst_data,
    )

    gst_pdf_api_res = mock_surepass_api_gst_pdf()
    s3_download_api_res = mock_s3_bucket_download(
        url=load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")[
            "data"
        ]["pdf_report"]
    )

    response = client.post(
        url,
        {"gst_number": valid_gst_number, "otp": valid_otp},
        content_type="application/json",
        **api_headers,
    )

    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()
    assert json_response["data"]["task_id"] is not None
    assert json_response["status"] == "success"
    assert json_response["code"] == status.HTTP_200_OK
    assert json_response["message"] == "OTP verified successfully."

    assert gst_pdf_api_res.call_count == 1
    assert s3_download_api_res.call_count == 1

    billing_account = BillingAccounts.objects.first()
    assert billing_account is not None
    kyc_qs = KYC.objects.filter(
        mode=KYCModeEnum.GST.value,
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        source=KYCSourceEnum.MYOPERATOR.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    assert kyc_qs.count() == 1
    kyc: KYC = kyc_qs.first()  # type: ignore
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.VERIFICATION.value  # type: ignore
    assert state.status == KYCStateStatusEnum.COMPLETED.value  # type: ignore
    assert state.data == gst_data["data"]  # type: ignore
    assert state.task_id == json_response["data"]["task_id"]
    assert state.failure_reason is None

    doc_qs = BillingAccountDocs.objects.filter(
        kyc=kyc,
        doc_type=gst_doc_type,
        doc_number=valid_gst_number,
        doc_name="pdf_report_gstin_1749201959545362",
        doc_ext="pdf",
        status=True,
    )
    assert doc_qs.count() == 1
    doc: BillingAccountDocs = doc_qs.first()  # type: ignore

    s3_obj_response = mock_s3_bucket.get_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=doc.doc_path,
    )
    assert s3_obj_response["Body"].read() == b"test content"

    cache_handler = GstCacheHandler(gst_number=valid_gst_number)
    assert cache_handler.get_otp_data() == {
        "phone_number": phone_number,
        "otp": valid_otp,
        "verification_attempts": "1",
        "verified": "true",
    }


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "request_data,expected_message, expected_error,expected_code",
    [
        (
            {"gst_number": "invalid_gst", "otp": "123456"},
            "Invalid GSTIN format. Please enter a valid 15-digit GST number.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
        (
            {"gst_number": "", "otp": "123456"},
            "Invalid GSTIN format. Please enter a valid 15-digit GST number.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
        (
            {"gst_number": None, "otp": "123456"},
            "Invalid GSTIN format. Please enter a valid 15-digit GST number.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
        (
            {"gst_number": "27AAPFU0939F1ZV", "otp": ""},
            "Invalid GSTIN format. Please enter a valid 15-digit GST number.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
        (
            {"gst_number": "27AAPFU0939F1ZV", "otp": None},
            "Invalid GSTIN format. Please enter a valid 15-digit GST number.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
        (
            {},
            "Invalid GSTIN format. Please enter a valid 15-digit GST number.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
    ],
)
def test_gst_verify_otp_invalid_input(
    client,
    url,
    request_data,
    expected_message,
    expected_error,
    expected_code,
    api_headers,
):
    response = client.post(
        url,
        request_data,
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "code": expected_code,
        "errors": {"detail": expected_error},
        "message": expected_message,
        "status": "error",
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_verify_otp_max_verification_attempts_exceeded(
    client,
    url,
    valid_gst_number,
    valid_otp,
    mock_gst_detail_api_with_custom_data,
    api_headers,
):
    phone_number = "**********"

    mock_gst_detail_api_with_custom_data(
        mobile_number=phone_number,
        gst_number=valid_gst_number,
    )
    # Setup cache with OTP data and max verification attempts
    cache_handler = GstCacheHandler(gst_number=valid_gst_number)
    cache_handler.set_otp_data(
        phone_number, valid_otp, cache_handler.MAX_VERIFY_ATTEMPTS
    )

    response = client.post(
        url,
        {"gst_number": valid_gst_number, "otp": valid_otp},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_429_TOO_MANY_REQUESTS,
        "message": f"OTP verification attempts exceeded. Maximum allowed: {cache_handler.MAX_VERIFY_ATTEMPTS}",
        "errors": {
            "detail": f"OTP verification attempts exceeded. Maximum allowed: {cache_handler.MAX_VERIFY_ATTEMPTS}"
        },
    }
    data = cache_handler.get_otp_data()
    assert data["verification_attempts"] == str(
        cache_handler.MAX_VERIFY_ATTEMPTS
    )
    assert data.get("verified") is None


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_verify_otp_no_otp_data_in_cache(
    client,
    url,
    valid_gst_number,
    valid_otp,
    mock_gst_detail_api_with_custom_data,
    api_headers,
):
    phone_number = "**********"

    mock_gst_detail_api_with_custom_data(
        mobile_number=phone_number,
        gst_number=valid_gst_number,
    )
    response = client.post(
        url,
        {"gst_number": valid_gst_number, "otp": valid_otp},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_400_BAD_REQUEST,
        "message": "OTP expired. Please request a new OTP.",
        "errors": {"detail": "OTP data not found or expired."},
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_verify_otp_invalid_otp(
    client,
    url,
    valid_gst_number,
    valid_otp,
    mock_gst_detail_api_with_custom_data,
    api_headers,
):
    phone_number = "**********"

    # Setup API response
    mock_gst_detail_api_with_custom_data(
        mobile_number=phone_number,
        gst_number=valid_gst_number,
    )
    # Setup cache with OTP data
    cache_handler = GstCacheHandler(gst_number=valid_gst_number)
    cache_handler.set_otp_data(phone_number, "654321", "0")  # Different OTP

    response = client.post(
        url,
        {"gst_number": valid_gst_number, "otp": valid_otp},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_400_BAD_REQUEST,
        "message": "Incorrect OTP. Please try again.",
        "errors": {"detail": "Input OTP does not match."},
    }

    data = cache_handler.get_otp_data()
    assert data["verification_attempts"] == "1"
    assert data.get("verified") is None


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "kyc_status",
    [
        KYCStatusEnum.PENDING.value,
        KYCStatusEnum.COMPLETED.value,
    ],
)
def test_gst_verify_otp_with_existing_kyc(
    client,
    valid_gst_number,
    mock_gst_detail_api_with_custom_data,
    api_headers,
    billing_account,
    kyc_status,
):
    """Test that verify OTP endpoint fails when there is an existing active KYC."""
    # Create an active KYC
    KYC.objects.create(billing_account=billing_account, status=kyc_status)

    url = reverse("kyc:gst_verify_otp")
    response = client.post(
        url,
        {"gst_number": valid_gst_number, "otp": "123456"},
        content_type="application/json",
        **api_headers,
    )

    assert response.status_code == status.HTTP_409_CONFLICT
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_409_CONFLICT,
        "message": "KYC is already in progress or completed",
        "errors": {"detail": "KYC is already in progress or completed"},
    }
