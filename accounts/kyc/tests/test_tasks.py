"""Tests for KYC tasks."""

import json
import re
from typing import Any

from django.conf import settings

import pytest
from rest_framework import status

from accounts.exceptions import (
    ExternalApiDependencyFailedException,
    ExternalAPIException,
)
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.exceptions import (
    InactiveGstNumberException,
    KycFailedException,
    KycFileDownloadException,
    KycNotFoundException,
)
from accounts.kyc.models import KYC
from accounts.kyc.tasks import (
    download_gst_pdf_task,
    fetch_gst_info_for_kyc_task,
)
from accounts.kyc.tests.factories import KYCFactory
from accounts.utils.common import get_trace_id


@pytest.mark.django_db
@pytest.mark.unittest
def test_download_gst_pdf(
    mock_s3_bucket: Any,
    mock_surepass_api_gst_pdf: Any,
    load_json: Any,
    mock_s3_bucket_download: Any,
    gst_doc_type: Any,
    billing_account: Any,
) -> None:
    """Test downloading GST PDF and verifying document creation.

    This test verifies that:
    1. GST PDF is downloaded successfully
    2. KYC status is updated to completed
    3. Document is created with correct details
    4. File is uploaded to S3
    """
    s3_download_api_res = mock_s3_bucket_download(
        url=load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")[
            "data"
        ]["pdf_report"]
    )
    # Mock responses
    success_response = load_json(
        "accounts/kyc/tests/fixtures/gst_pdf_success.json"
    )
    gst_pdf_res = mock_surepass_api_gst_pdf(success_response)

    # Setup test data
    kyc_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"]
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.GST, billing_account=billing_account, data=kyc_data
    )
    gst_number = kyc_data["gstin"]

    # Execute task
    task = download_gst_pdf_task.apply_async(
        args=[gst_number, kyc.id, KYCStateEnum.VERIFICATION.name],
        kwargs={"fail_kyc": True},
    )  # type: ignore
    assert task.successful()

    # Verify results
    kyc.refresh_from_db()
    assert kyc.status == KYCStatusEnum.PENDING.value
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.COMPLETED.value
    assert latest_state.failure_reason is None

    # Verify document creation
    doc = kyc.billing_account.get_docs().first()
    assert doc is not None
    assert doc.doc_type == gst_doc_type
    assert doc.doc_number == gst_number
    assert doc.kyc == kyc
    assert doc.doc_name == "pdf_report_gstin_1749201959545362"
    assert doc.doc_ext == "pdf"
    assert doc.doc_path.startswith("companydoc/")

    # Verify S3 upload
    response = mock_s3_bucket.get_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=doc.doc_path
    )
    assert response["Body"].read() == b"test content"

    assert s3_download_api_res.call_count == 1
    assert gst_pdf_res.call_count == 1


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "fail_kyc",
    [
        True,
        False,
    ],
)
def test_download_gst_pdf_api_failure(
    mock_s3_bucket: Any,
    mock_surepass_api_gst_pdf: Any,
    load_json: Any,
    billing_account: Any,
    fail_kyc: bool,
) -> None:
    """Test GST PDF download when Surepass API call fails.

    This test verifies that:
    1. Task handles API failure gracefully
    2. KYC status is updated to failed
    3. Error is logged appropriately
    """
    # Mock API failure
    error_response = load_json(
        "accounts/kyc/tests/fixtures/gst_pdf_invalid_gst_number.json"
    )
    mock_surepass_api_gst_pdf(error_response, status_code=400)
    kyc_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"]
    # Setup test data
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.GST, billing_account=billing_account, data=kyc_data
    )

    gst_number = kyc_data["gstin"]

    # Execute task and expect exception
    with pytest.raises(
        KycFileDownloadException, match="Failed to download GST PDF:"
    ):
        task = download_gst_pdf_task.apply_async(
            args=[gst_number, kyc.id, KYCStateEnum.VERIFICATION.name],
            kwargs={"fail_kyc": fail_kyc},
        )  # type: ignore
        assert not task.successful()

    # Verify results
    kyc.refresh_from_db()
    assert (
        kyc.status == KYCStatusEnum.FAILED.value
        if fail_kyc
        else kyc.status == KYCStatusEnum.PENDING.value
    )
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.failure_reason and (
        json.loads(latest_state.failure_reason)
        == {
            "reason": f"Failed to download GST PDF: {json.dumps(error_response)}",
            "trace_id": get_trace_id(),
            "exception": "KycFileDownloadException",
        }
    )


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "fail_kyc",
    [
        True,
        False,
    ],
)
def test_download_gst_pdf_file_download_failure(
    mock_s3_bucket: Any,
    mock_surepass_api_gst_pdf: Any,
    load_json: Any,
    billing_account: Any,
    mock_s3_bucket_download: Any,
    gst_doc_type: Any,
    fail_kyc: bool,
) -> None:
    """Test GST PDF download when file download from URL fails.

    This test verifies that:
    1. Task handles file download failure gracefully
    2. KYC status is updated to failed
    3. Error is logged appropriately
    """
    gst_pdf_expected_res = load_json(
        "accounts/kyc/tests/fixtures/gst_pdf_success.json"
    )
    # file not found on S3 bucket
    s3_download_api_res = mock_s3_bucket_download(
        url=load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")[
            "data"
        ]["pdf_report"],
        status_code=status.HTTP_404_NOT_FOUND,
    )
    gst_pdf_res = mock_surepass_api_gst_pdf(gst_pdf_expected_res)
    pdf_url = gst_pdf_expected_res["data"]["pdf_report"]

    kyc_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"]
    # Setup test data
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.GST, billing_account=billing_account, data=kyc_data
    )
    gst_number = kyc_data["gstin"]

    # Execute task and expect exception
    with pytest.raises(ExternalAPIException, match="404 Client Error"):
        task = download_gst_pdf_task.apply_async(
            args=[gst_number, kyc.id, KYCStateEnum.VERIFICATION.name],
            kwargs={"fail_kyc": fail_kyc},
        )  # type: ignore
        assert not task.successful()

    # Verify results
    kyc.refresh_from_db()
    assert (
        kyc.status == KYCStatusEnum.FAILED.value
        if fail_kyc
        else kyc.status == KYCStatusEnum.PENDING.value
    )
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.failure_reason and (
        json.loads(latest_state.failure_reason)
        == {
            "reason": f"404 Client Error: Not Found for url: {pdf_url}",
            "trace_id": get_trace_id(),
            "exception": "ExternalAPIException",
        }
    )
    assert gst_pdf_res.call_count == 1
    assert s3_download_api_res.call_count == 1


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "fail_kyc",
    [
        True,
        False,
    ],
)
def test_download_gst_pdf_s3_upload_failure(
    mock_surepass_api_gst_pdf: Any,
    load_json: Any,
    billing_account: Any,
    mock_s3_bucket_download,
    fail_kyc: bool,
) -> None:
    """Test GST PDF download when S3 upload fails.

    This test verifies that:
    1. Task handles S3 upload failure gracefully
    2. KYC status is updated to failed
    3. Error is logged appropriately
    """
    s3_download_api_res = mock_s3_bucket_download(
        url=load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")[
            "data"
        ]["pdf_report"]
    )
    # Mock responses
    success_response = load_json(
        "accounts/kyc/tests/fixtures/gst_pdf_success.json"
    )
    gst_pdf_res = mock_surepass_api_gst_pdf(success_response)

    kyc_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"]
    # Setup test data
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.GST, billing_account=billing_account, data=kyc_data
    )
    gst_number = kyc_data["gstin"]

    # Execute task and expect exception
    with pytest.raises(KycFailedException, match="S3 file upload failed."):
        task = download_gst_pdf_task.apply_async(
            args=[gst_number, kyc.id, KYCStateEnum.VERIFICATION.name],
            kwargs={"fail_kyc": fail_kyc},
        )  # type: ignore
        assert not task.successful()

    # Verify results
    kyc.refresh_from_db()

    assert (
        kyc.status == KYCStatusEnum.FAILED.value
        if fail_kyc
        else kyc.status == KYCStatusEnum.PENDING.value
    )
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.failure_reason and (
        json.loads(latest_state.failure_reason)
        == {
            "reason": "S3 file upload failed.",
            "trace_id": get_trace_id(),
            "exception": "KycFailedException",
        }
    )
    assert gst_pdf_res.call_count == 1
    assert s3_download_api_res.call_count == 1


@pytest.mark.django_db
@pytest.mark.unittest
def test_download_gst_pdf_kyc_not_found(
    mock_surepass_api_gst_pdf: Any,
    load_json: Any,
) -> None:
    """Test GST PDF download when KYC object is not found.

    This test verifies that:
    1. Task handles non-existent KYC gracefully
    2. Appropriate exception is raised
    """
    # Mock successful API response
    success_response = load_json(
        "accounts/kyc/tests/fixtures/gst_pdf_success.json"
    )
    mock_surepass_api_gst_pdf(success_response)

    # Execute task with non-existent KYC ID
    with pytest.raises(
        KycNotFoundException,
        match=re.escape("KYC (id: non-existent-kyc-id) not found"),
    ):
        task = download_gst_pdf_task.apply_async(  # type: ignore
            args=[
                "27AAPFU0939F1ZV",
                "non-existent-kyc-id",
                KYCStateEnum.VERIFICATION.name,
            ],
            kwargs={"fail_kyc": True},
        )
        assert not task.successful()


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "fail_kyc",
    [
        True,
        False,
    ],
)
def test_download_gst_pdf_missing_required_fields(
    mock_surepass_api_gst_pdf: Any,
    load_json: Any,
    billing_account: Any,
    fail_kyc: bool,
) -> None:
    # Mock API failure
    error_response = load_json(
        "accounts/kyc/tests/fixtures/gst_pdf_gst_or_client_id_is_req.json"
    )
    mock_surepass_api_gst_pdf(error_response, status_code=400)

    # Setup test data with empty GST number
    kyc_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"]
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.GST, billing_account=billing_account, data=kyc_data
    )
    gst_number = ""  # Empty GST number to trigger the error

    # Execute task and expect exception
    with pytest.raises(
        KycFileDownloadException, match="Failed to download GST PDF:"
    ):
        task = download_gst_pdf_task.apply_async(
            args=[gst_number, kyc.id, KYCStateEnum.VERIFICATION.name],
            kwargs={"fail_kyc": fail_kyc},
        )  # type: ignore
        assert not task.successful()

    # Verify results
    kyc.refresh_from_db()
    assert (
        kyc.status == KYCStatusEnum.FAILED.value
        if fail_kyc
        else kyc.status == KYCStatusEnum.PENDING.value
    )
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.failure_reason and (
        json.loads(latest_state.failure_reason)
        == {
            "reason": f"Failed to download GST PDF: {json.dumps(error_response)}",
            "trace_id": get_trace_id(),
            "exception": "KycFileDownloadException",
        }
    )


@pytest.mark.django_db
@pytest.mark.unittest
def test_fetch_gst_info_for_kyc_task(
    load_json: Any,
    billing_account: Any,
    mock_gst_detail_api_with_custom_data: Any,
    valid_gst_number: Any,
):
    phone_number = "**********"
    gst_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")
    # Setup API response
    gst_detail_api_res = mock_gst_detail_api_with_custom_data(
        mobile_number=phone_number,
        gst_number=valid_gst_number,
        expected_res=gst_data,
    )
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.GST.value,  # GST mode instead of AADHAAR
        current_state=KYCStateEnum.GST_INFO.value,
    )
    kyc.create_kyc_state(data={})

    fetch_gst_info_for_kyc_task.apply_async(args=[valid_gst_number, kyc.id, KYCStateEnum.GST_INFO.name])  # type: ignore

    assert gst_detail_api_res.call_count == 1
    assert kyc.status == KYCStatusEnum.PENDING.value
    assert kyc.current_state == KYCStateEnum.GST_INFO.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.GST_INFO.value
    assert latest_state.status == KYCStateStatusEnum.PENDING.value
    assert latest_state.data == gst_data["data"]


@pytest.mark.django_db
@pytest.mark.unittest
def test_fetch_gst_info_for_kyc_task_kyc_not_found(
    load_json: Any,
    billing_account: Any,
    mock_gst_detail_api_with_custom_data: Any,
    valid_gst_number: Any,
):
    phone_number = "**********"
    gst_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")
    # Setup API response
    gst_detail_api_res = mock_gst_detail_api_with_custom_data(
        mobile_number=phone_number,
        gst_number=valid_gst_number,
        expected_res=gst_data,
    )
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.GST.value,  # GST mode instead of AADHAAR
        current_state=KYCStateEnum.GST_INFO.value,
    )
    kyc.create_kyc_state(data={})
    with pytest.raises(
        KycNotFoundException, match=re.escape("KYC (id: 12234) not found")
    ):
        task = fetch_gst_info_for_kyc_task.apply_async(args=[valid_gst_number, "12234", KYCStateEnum.GST_INFO.name])  # type: ignore
        assert not task.successful()

    assert gst_detail_api_res.call_count == 0
    assert kyc.status == KYCStatusEnum.PENDING.value
    assert kyc.current_state == KYCStateEnum.GST_INFO.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.GST_INFO.value
    assert latest_state.status == KYCStateStatusEnum.PENDING.value
    assert latest_state.data == {}


@pytest.mark.django_db
@pytest.mark.unittest
def test_fetch_gst_info_for_kyc_task_gst_api_failed(
    load_json: Any,
    billing_account: Any,
    mock_gst_detail_api_with_custom_data: Any,
    valid_gst_number: Any,
):
    # Setup API response
    gst_detail_api_res = mock_gst_detail_api_with_custom_data(
        expected_res={"error": "Invalid GSTIN number"},
        status_code=status.HTTP_400_BAD_REQUEST,
    )
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.GST.value,  # GST mode instead of AADHAAR
        current_state=KYCStateEnum.GST_INFO.value,
    )
    kyc.create_kyc_state(data={})
    with pytest.raises(
        ExternalApiDependencyFailedException,
        match="Unable to verify GST at the moment. Please try again or complete KYC using PAN.",
    ):
        task = fetch_gst_info_for_kyc_task.apply_async(args=[valid_gst_number, kyc.id, KYCStateEnum.GST_INFO.name])  # type: ignore
        assert not task.successful()

    assert gst_detail_api_res.call_count == 1
    assert kyc.status == KYCStatusEnum.PENDING.value
    assert kyc.current_state == KYCStateEnum.GST_INFO.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.GST_INFO.value
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.failure_reason and (
        json.loads(latest_state.failure_reason)
        == {
            "reason": "Unable to verify GST at the moment. Please try again or complete KYC using PAN.",
            "trace_id": get_trace_id(),
            "exception": "ExternalApiDependencyFailedException",
        }
    )


@pytest.mark.django_db
@pytest.mark.unittest
def test_fetch_gst_info_for_kyc_task_gst_inactive(
    load_json: Any,
    billing_account: Any,
    mock_gst_detail_api_with_custom_data: Any,
    valid_gst_number: Any,
):
    # Setup API response
    gst_detail_api_res = mock_gst_detail_api_with_custom_data(
        gst_status="inactive",
    )
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.GST.value,  # GST mode instead of AADHAAR
        current_state=KYCStateEnum.GST_INFO.value,
    )
    kyc.create_kyc_state(data={})
    with pytest.raises(
        InactiveGstNumberException,
        match="Your GST is inactive. Please use a valid GST or complete KYC via PAN.",
    ):
        task = fetch_gst_info_for_kyc_task.apply_async(args=[valid_gst_number, kyc.id, KYCStateEnum.GST_INFO.name])  # type: ignore
        assert not task.successful()

    assert gst_detail_api_res.call_count == 1
    assert kyc.status == KYCStatusEnum.PENDING.value
    assert kyc.current_state == KYCStateEnum.GST_INFO.value
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.GST_INFO.value
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.failure_reason and (
        json.loads(latest_state.failure_reason)
        == {
            "reason": "Your GST is inactive. Please use a valid GST or complete KYC via PAN.",
            "trace_id": get_trace_id(),
            "exception": "InactiveGstNumberException",
        }
    )
