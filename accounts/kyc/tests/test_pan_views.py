from django.conf import settings
from django.urls import reverse

import pytest
from rest_framework import status

from accounts.billing_accounts.models import BillingAccountDocs, BillingAccounts
from accounts.kyc.cache_handler import <PERSON><PERSON>haarCacheHandler
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCSourceEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC
from accounts.kyc.tests.factories import KYCFactory




@pytest.mark.django_db
@pytest.mark.unittest
def test_pan_upload_api_requires_valid_billing_account_id_in_header(
    client, pan_upload_url
):
    
    url = reverse("kyc:gst_non_otp", kwargs={"kyc_id": kyc.id})
    


    response = client.post(
        pan_upload_url,
        {"s3_key": ""},
        content_type="application/json",
    )
    # assert API response
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {
        "status": "error",
        "code": "permission_denied",
        "message": "You do not have permission to perform this action.",
        "errors": {"detail": "Invalid billing_account_id in headers!"},
    }



@pytest.mark.parametrize(
    "kyc_status",
    [KYCStatusEnum.EXPIRED.value, KYCStatusEnum.FAILED.value, None],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_send_otp_success(
    client,
    aadhaar_send_otp_url,
    api_headers,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
    billing_account,
    mock_aadhaar_send_otp_api,
    kyc_status,
):
    """
    Test that the API returns a 200 status code and the correct response when the request is valid.
    Also test that the cache is set correctly.
    """

    if kyc_status:
        KYCFactory(
            billing_account=billing_account,
            mode=KYCModeEnum.AADHAAR.value,
            status=kyc_status,
        )

    mock_aadhaar_send_otp_api(
        expected_response={
            "data": {
                "client_id": valid_aadhaar_client_id,
                "otp_sent": True,
                "if_number": True,
                "valid_aadhaar": True,
                "status": "generate_otp_success",
            },
            "status_code": status.HTTP_200_OK,
            "message_code": "success",
            "message": "OTP Sent.",
            "success": True,
        },
        status_code=status.HTTP_200_OK,
    )
    response = client.post(
        aadhaar_send_otp_url,
        {"aadhaar_number": valid_aadhaar_number},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "status": "success",
        "code": status.HTTP_200_OK,
        "message": "OTP sent successfully on the phone number linked to Aadhaar number.",
        "data": None,
    }
    # assert cache data
    assert AadhaarCacheHandler(key=valid_aadhaar_number).fetch_otp_data() == {
        "verification_attempts": "0",
        "client_id": valid_aadhaar_client_id,
    }


