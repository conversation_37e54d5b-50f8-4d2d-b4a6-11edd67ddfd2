from dataclasses import asdict

from django.conf import settings
from django.urls import reverse

import pytest
from rest_framework import status

from accounts.billing_accounts.models import BillingAccountDocs
from accounts.kyc.cache_handler import PanCacheHandler
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC
from accounts.kyc.parsers import PanOCRParser


@pytest.mark.django_db
@pytest.mark.unittest
def test_pan_upload_api_requires_valid_billing_account_id_in_header(client):
    """Test that the API requires a valid billing account ID in the header."""

    response = client.post(
        reverse("kyc:pan-upload", kwargs={"kyc_id": "1"}),
        {"s3_key": ""},
        content_type="application/json",
    )
    # assert API response
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {
        "status": "error",
        "code": "permission_denied",
        "message": "You do not have permission to perform this action.",
        "errors": {"detail": "Invalid billing_account_id in headers!"},
    }


@pytest.mark.parametrize("s3_key", [None, "", " "])
@pytest.mark.django_db
@pytest.mark.unittest
def test_pan_upload_api_with_invalid_s3_key_returns_http_400(
    client, api_headers, s3_key
):
    """Test that the API returns a 400 status code and the correct response when the request is invalid."""
    response = client.post(
        reverse("kyc:pan-upload", kwargs={"kyc_id": "1"}),
        {"s3_key": s3_key},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_400_BAD_REQUEST,
        "message": "No or invalid s3_key provided. Expected as required string.",
        "errors": {
            "detail": "No or invalid s3_key provided. Expected as required string."
        },
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_pan_upload_api_with_invalid_or_non_existent_kyc_id_returns_http_404(
    client, api_headers
):
    """Test that the API returns a 404 status code and the correct response when the request is invalid."""
    response = client.post(
        reverse("kyc:pan-upload", kwargs={"kyc_id": "1"}),
        {"s3_key": "any_pan_file.png"},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_404_NOT_FOUND,
        "message": "KYC not found.",
        "errors": {"detail": "KYC not found."},
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_pan_upload_api_with_no_s3_file_exist_returns_http_404(
    client, api_headers, create_kyc_for_state, mock_s3_bucket
):
    """Test that the API returns a 404 status code and the correct response when the S3 file does not exist."""
    kyc: KYC = create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.VERIFICATION,
        state_data={"data": "data"},
    )
    pan_file_name_with_ext = "any_pan_file.png"
    response = client.post(
        reverse("kyc:pan-upload", kwargs={"kyc_id": kyc.id}),
        {"s3_key": pan_file_name_with_ext},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_404_NOT_FOUND,
        "message": f"AWS S3 key: {pan_file_name_with_ext} does not exist.",
        "errors": {"detail": "PAN object not found."},
    }


@pytest.mark.parametrize("extension", [".png", ".jpg", ".jpeg"])
@pytest.mark.django_db
@pytest.mark.unittest
def test_pan_upload_api_when_upload_attempts_exceeded_returns_http_429(
    client,
    api_headers,
    valid_pan_file_in_s3_tmp,
    create_kyc_for_state,
    extension,
):
    """Test that the API returns a 429 status code and the correct response when the upload attempts are exceeded."""
    # Create KYC in verification state
    kyc: KYC = create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.VERIFICATION,
        state_data={"data": "data"},
    )
    pan_cache_handler = PanCacheHandler(key=kyc.id)
    for _ in range(pan_cache_handler.PAN_UPLOAD_ATTEMPTS_LIMIT):
        pan_cache_handler.incr_pan_upload_attempts()
    assert pan_cache_handler.is_pan_upload_attempt_allowed() is False
    s3_key = valid_pan_file_in_s3_tmp(extension)
    response = client.post(
        reverse("kyc:pan-upload", kwargs={"kyc_id": kyc.id}),
        {"s3_key": s3_key},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_429_TOO_MANY_REQUESTS,
        "message": "PAN upload attempts exceeded. Maximum allowed: %s"
        % pan_cache_handler.PAN_UPLOAD_ATTEMPTS_LIMIT,
        "errors": {
            "detail": "PAN upload attempts exceeded. Maximum allowed: %s"
            % pan_cache_handler.PAN_UPLOAD_ATTEMPTS_LIMIT
        },
    }


@pytest.mark.parametrize("extension", [".png", ".jpg", ".jpeg"])
@pytest.mark.django_db
@pytest.mark.unittest
def test_pan_upload_api_success(
    client,
    api_headers,
    valid_pan_file_in_s3_tmp,
    create_kyc_for_state,
    load_json,
    pan_doc_type,
    mock_s3_bucket,
    mock_surepass_fetch_pan_info_from_file_api,
    extension,
):
    """Test that the API returns a 200 status code and the correct response when the request is valid."""
    # Create KYC in verification state
    kyc: KYC = create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.VERIFICATION,
        state_data={"data": "data"},
    )
    s3_key = valid_pan_file_in_s3_tmp(extension)
    # upload file to S3
    mock_s3_bucket.put_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=s3_key,
        Body=b"test content",
    )
    # mock fetch pan info from file API response
    pan_data = load_json(
        "accounts/kyc/tests/fixtures/pan_ocr_200_success_response.json"
    )
    pan_number = pan_data["data"]["ocr_fields"][0]["pan_number"]["value"]
    mocked_surepass_fetch_pan_info_from_file_api = (
        mock_surepass_fetch_pan_info_from_file_api(
            expected_response=pan_data, status_code=status.HTTP_200_OK
        )
    )
    response = client.post(
        reverse("kyc:pan-upload", kwargs={"kyc_id": kyc.id}),
        {"s3_key": s3_key},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()
    assert json_response["status"] == "success"
    assert json_response["code"] == status.HTTP_200_OK
    assert (
        json_response["message"] == "PAN upload process started successfully."
    )
    assert json_response["data"]["task_id"] is not None
    # assert pan upload attempts in cache
    pan_cache_handler = PanCacheHandler(key=kyc.id)
    assert pan_cache_handler.get() == 1
    # assert API calls
    assert mocked_surepass_fetch_pan_info_from_file_api.call_count == 1
    # assert database
    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.UPLOAD_PAN.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.UPLOAD_PAN.value
    assert state.status == KYCStateStatusEnum.COMPLETED.value
    assert state.data == asdict(PanOCRParser(pan_data).parse())
    assert state.task_id == json_response["data"]["task_id"]
    assert state.failure_reason is None
    assert state.started_at
    assert state.completed_at

    doc_qs = BillingAccountDocs.objects.filter(
        kyc=kyc,
        doc_type=pan_doc_type,
        doc_number=pan_number,
        doc_name=pan_number,
        doc_ext=extension.split(".")[-1],
        status=True,
    )
    assert doc_qs.count() == 1
