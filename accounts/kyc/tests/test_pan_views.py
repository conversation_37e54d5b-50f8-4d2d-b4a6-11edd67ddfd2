from django.conf import settings
from django.urls import reverse

import pytest
from rest_framework import status

from accounts.billing_accounts.models import BillingAccountDocs, BillingAccounts
from accounts.kyc.cache_handler import <PERSON><PERSON>haarCacheHandler
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCSourceEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC
from accounts.kyc.tests.factories import KYCFactory




@pytest.mark.django_db
@pytest.mark.unittest
def test_pan_upload_api_requires_valid_billing_account_id_in_header(
    client
):
    """Test that the API requires a valid billing account ID in the header."""
    
    response = client.post(
        reverse("kyc:pan-upload", kwargs={"kyc_id": "1"}),
        {"s3_key": ""},
        content_type="application/json",
    )
    # assert API response
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {
        "status": "error",
        "code": "permission_denied",
        "message": "You do not have permission to perform this action.",
        "errors": {"detail": "Invalid billing_account_id in headers!"},
    }

