import json

from django.urls import reverse

import pytest
from rest_framework import status

from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC, KYCState
from accounts.kyc.tests.factories import KYCFactory


@pytest.fixture
def url():
    return reverse("kyc:video_kyc_init", kwargs={"kyc_id": "test_kyc_id"})


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "mode,state",
    [
        (KYCModeEnum.GST, KYCStateEnum.VERIFICATION),
        (KYCModeEnum.AADHAAR, KYCStateEnum.GST_INFO),
        (KYCModeEnum.AADHAAR, KYCStateEnum.UPLOAD_PAN),
        (KYCModeEnum.AADHAAR, KYCStateEnum.DIGILOCKER_PAN),
    ],
    ids=[
        "gst_verification",
        "aadhaar_gst_info",
        "aadhaar_upload_pan",
        "aadhaar_digilocker_pan",
    ],
)
def test_video_kyc_init_success(
    client,
    url,
    api_headers,
    load_json,
    mock_surepass_api_video_kyc,
    create_kyc_for_state,
    mode,
    state,
):
    """Test successful video KYC initialization."""
    # Create KYC in verification state
    kyc: KYC = create_kyc_for_state(
        kyc_mode=mode,
        kyc_state=KYCStateEnum.VERIFICATION,
        state_data={"data": "data"},
    )
    if state != KYCStateEnum.VERIFICATION:
        kyc: KYC = create_kyc_for_state(
            kyc_mode=mode, kyc_state=state, kyc=kyc, state_data={"data": "data"}
        )

    # Mock video KYC API response
    video_kyc_data = load_json(
        "accounts/kyc/tests/fixtures/video_kyc_init_200.json"
    )
    mock_video_kyc_api = mock_surepass_api_video_kyc(
        expected_response=video_kyc_data,
        status_code=status.HTTP_200_OK,
    )

    payload = {
        "eye_blink": False,
        "assist_voice": False,
        "head_movement_attempts": 4,
        "frame_size": "medium",
    }
    # Make request
    response = client.post(
        url.replace("test_kyc_id", str(kyc.id)),
        payload,
        content_type="application/json",
        **api_headers,
    )

    # Assert response
    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()
    assert json_response["status"] == "success"
    assert json_response["code"] == status.HTTP_200_OK
    assert json_response["message"] == "Liveness initialized successfully."
    assert json_response["data"] == video_kyc_data["data"]

    assert mock_video_kyc_api.call_count == 1
    kyc.refresh_from_db()
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.VIDEO_KYC.value
    assert state.status == KYCStateStatusEnum.COMPLETED.value
    assert state.data == video_kyc_data["data"]
    assert state.failure_reason is None
    assert kyc.status == KYCStatusEnum.PENDING.value
    assert kyc.current_state == KYCStateEnum.VIDEO_KYC.value


@pytest.mark.django_db
@pytest.mark.unittest
def test_video_kyc_init_kyc_not_found(
    client,
    url,
    api_headers,
):
    """Test video KYC initialization when KYC is not found."""
    payload = {
        "eye_blink": False,
        "assist_voice": False,
        "head_movement_attempts": 4,
        "frame_size": "medium",
    }
    response = client.post(
        url,
        payload,
        content_type="application/json",
        **api_headers,
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_404_NOT_FOUND,
        "message": "KYC not found.",
        "errors": {"detail": "KYC not found."},
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_video_kyc_init_transition_not_allowed(
    client,
    url,
    api_headers,
    billing_account,
):
    """Test video KYC initialization when transition is not allowed."""
    # Create KYC in verification state but don't mark it as completed
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.AADHAAR.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    kyc.create_kyc_state(data={})
    payload = {
        "eye_blink": False,
        "assist_voice": False,
        "head_movement_attempts": 4,
        "frame_size": "medium",
    }

    response = client.post(
        url.replace("test_kyc_id", str(kyc.id)),
        payload,
        content_type="application/json",
        **api_headers,
    )

    assert response.status_code == status.HTTP_412_PRECONDITION_FAILED
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_412_PRECONDITION_FAILED,
        "message": f"Not allowed to transition from current state. Current state: {KYCStateEnum.VERIFICATION.value}",
        "errors": {
            "detail": f"Not allowed to transition from current state. Current state: {KYCStateEnum.VERIFICATION.value}, kyc_id: {kyc.id}"
        },
    }


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "expected_response, expected_status_code, expected_message",
    [
        (
            "accounts/kyc/tests/fixtures/video_kyc_invalid_token_401.json",
            status.HTTP_401_UNAUTHORIZED,
            "Failed to initialize video KYC.",
        ),
        (
            "accounts/kyc/tests/fixtures/video_kyc_init_200_invalid_data.json",
            status.HTTP_200_OK,
            "Invalid video KYC response.",
        ),
        (
            json.JSONDecodeError("test", "test", 0),
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            "Failed to parse Surepass Init Video KYC API response as JSON.",
        ),
    ],
    ids=[
        "invalid_token",
        "invalid_data",
        "json_decode_error",
    ],
)
def test_video_kyc_init_surepass_api_error(
    client,
    url,
    api_headers,
    load_json,
    billing_account,
    mock_surepass_api_video_kyc,
    expected_response,
    expected_message,
    expected_status_code,
):
    """Test video KYC initialization with invalid request data."""
    # Create KYC in verification state
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.GST.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    kyc.create_kyc_state(data={"data": "data"})
    kyc.mark_latest_state_as_completed()

    if isinstance(expected_response, str):
        expected_response = load_json(expected_response)

    mock_video_kyc_api = mock_surepass_api_video_kyc(
        expected_response=expected_response,
        status_code=expected_status_code,
    )

    payload = {
        "eye_blink": False,
        "assist_voice": False,
        "head_movement_attempts": 4,
        "frame_size": "medium",
    }
    response = client.post(
        url.replace("test_kyc_id", str(kyc.id)),
        payload,
        content_type="application/json",
        **api_headers,
    )
    assert mock_video_kyc_api.call_count == 1
    assert response.status_code == status.HTTP_424_FAILED_DEPENDENCY
    json_response = response.json()
    assert json_response["status"] == "error"
    assert json_response["code"] == status.HTTP_424_FAILED_DEPENDENCY
    assert json_response["message"] == expected_message
    assert json_response["errors"] == {
        "detail": "External API dependency failed."
    }

    assert mock_video_kyc_api.call_count == 1
    kyc.refresh_from_db()
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.VERIFICATION.value
    assert state.status == KYCStateStatusEnum.COMPLETED.value
    assert state.failure_reason is None
    assert kyc.status == KYCStatusEnum.PENDING.value
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value


@pytest.mark.unittest
@pytest.mark.django_db
def test_video_kyc_init_require_valid_billing_account_id_in_header(
    client,
    url,
    billing_account,
):
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.GST.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    kyc.create_kyc_state(data={"data": "data"})
    kyc.mark_latest_state_as_completed()

    payload = {
        "eye_blink": False,
        "assist_voice": False,
        "head_movement_attempts": 4,
        "frame_size": "medium",
    }
    response = client.post(
        url.replace("test_kyc_id", str(kyc.id)),
        payload,
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {
        "status": "error",
        "code": "permission_denied",
        "message": "You do not have permission to perform this action.",
        "errors": {"detail": "Invalid billing_account_id in headers!"},
    }
