import pytest

from accounts.kyc.exceptions import (
    KycFileDownloadParserException,
    PanOCRParserException,
)
from accounts.kyc.parsers import (
    AadhaarFileDownloadParser,
    GstFileDownloadParser,
    PanOCRParser,
)


@pytest.mark.unittest
def test_kyc_file_download_parser_success(load_json):
    """Test successful parsing of KYC file download response."""
    data = load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")

    parser = GstFileDownloadParser(data)
    result = parser.parse()

    assert result.pdf_url == data["data"]["pdf_report"]
    assert result.client_id == data["data"]["client_id"]
    assert result.gstin == data["data"]["gstin"]


@pytest.mark.unittest
def test_kyc_file_download_parser_missing_data():
    """Test parser with missing data field."""
    data = {}

    parser = GstFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert str(exc_info.value) == "No data found in the response"


@pytest.mark.unittest
def test_kyc_file_download_parser_missing_pdf_url(load_json):
    """Test parser with missing PDF URL."""
    data = load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")
    del data["data"]["pdf_report"]

    parser = GstFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert str(exc_info.value) == "No PDF URL found in the response"


@pytest.mark.unittest
def test_kyc_file_download_parser_missing_client_id(load_json):
    """Test parser with missing client ID."""
    data = load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")
    del data["data"]["client_id"]

    parser = GstFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert str(exc_info.value) == "No client ID found in the response"


@pytest.mark.unittest
def test_kyc_file_download_parser_missing_gstin(load_json):
    """Test parser with missing GSTIN."""
    data = load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")
    del data["data"]["gstin"]

    parser = GstFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert str(exc_info.value) == "No GSTIN found in the response"


@pytest.mark.unittest
def test_aadhaar_file_download_parser_success(load_json):
    """Test successful parsing of Aadhaar file download response."""

    data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )

    parser = AadhaarFileDownloadParser(data)
    result = parser.parse()

    assert result.pdf_url == data["data"]["aadhaar_pdf"]
    assert result.client_id == data["data"]["client_id"]


@pytest.mark.unittest
def test_aadhaar_file_download_parser_missing_data():
    """Test parser with missing data field."""

    data = {}

    parser = AadhaarFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert (
        str(exc_info.value)
        == "No data found in the Aadhaar PDF download API response"
    )


@pytest.mark.unittest
def test_aadhaar_file_download_parser_missing_pdf_url(load_json):
    """Test parser with missing PDF URL."""

    data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )
    del data["data"]["aadhaar_pdf"]

    parser = AadhaarFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert (
        str(exc_info.value)
        == "No PDF URL found in the Aadhaar PDF download API response"
    )


@pytest.mark.unittest
def test_aadhaar_file_download_parser_missing_client_id(load_json):
    """Test parser with missing client ID."""

    data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )
    del data["data"]["client_id"]

    parser = AadhaarFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert (
        str(exc_info.value)
        == "No client id found in the Aadhaar PDF download API response"
    )


@pytest.mark.unittest
def test_pan_ocr_parser_success(load_json):
    """Test successful parsing of PAN OCR response."""

    data = load_json("accounts/kyc/tests/fixtures/pan_ocr_success.json")
    parser = PanOCRParser(data)
    result = parser.parse()
    data = data["data"]
    assert result.client_id == data["client_id"]
    ocr_fields = data["ocr_fields"][0]
    assert result.pan_number == ocr_fields["pan_number"]["value"]
    assert result.full_name == ocr_fields["full_name"]["value"]
    assert result.father_name == ocr_fields["father_name"]["value"]
    assert result.dob == ocr_fields["dob"]["value"]


@pytest.mark.unittest
def test_pan_ocr_parser_missing_data(load_json):
    """Test parser with missing data field."""

    data = {}
    parser = PanOCRParser(data)
    with pytest.raises(
        PanOCRParserException,
        match=r"\[PanOCRParser\] No or invalid data found in the PAN OCR API response.",
    ):
        parser.parse()


@pytest.mark.unittest
def test_pan_ocr_parser_missing_client_id(load_json):
    """Test parser with missing client ID."""

    data = load_json("accounts/kyc/tests/fixtures/pan_ocr_success.json")
    del data["data"]["client_id"]
    parser = PanOCRParser(data)
    with pytest.raises(
        PanOCRParserException,
        match=r"\[PanOCRParser\] No or invalid client ID found in the PAN OCR API response.",
    ):
        parser.parse()


@pytest.mark.unittest
def test_pan_ocr_parser_missing_ocr_fields(load_json):
    """Test parser with missing OCR fields."""

    data = load_json("accounts/kyc/tests/fixtures/pan_ocr_success.json")
    del data["data"]["ocr_fields"]
    parser = PanOCRParser(data)
    with pytest.raises(
        PanOCRParserException,
        match=r"\[PanOCRParser\] No or invalid OCR fields found in the PAN OCR API response.",
    ):
        parser.parse()


@pytest.mark.unittest
def test_pan_ocr_parser_missing_pan_number(load_json):
    """Test parser with missing PAN number."""

    data = load_json("accounts/kyc/tests/fixtures/pan_ocr_success.json")
    del data["data"]["ocr_fields"][0]["pan_number"]
    parser = PanOCRParser(data)
    with pytest.raises(
        PanOCRParserException,
        match=r"\[PanOCRParser\] No or invalid PAN number found in the PAN OCR API response.",
    ):
        parser.parse()


@pytest.mark.unittest
def test_pan_ocr_parser_missing_full_name(load_json):
    """Test parser with missing full name."""

    data = load_json("accounts/kyc/tests/fixtures/pan_ocr_success.json")
    del data["data"]["ocr_fields"][0]["full_name"]
    parser = PanOCRParser(data)
    with pytest.raises(
        PanOCRParserException,
        match=r"\[PanOCRParser\] No or invalid full name found in the PAN OCR API response.",
    ):
        parser.parse()


@pytest.mark.unittest
def test_pan_ocr_parser_missing_father_name(load_json):
    """Test parser with missing father name."""

    data = load_json("accounts/kyc/tests/fixtures/pan_ocr_success.json")
    del data["data"]["ocr_fields"][0]["father_name"]
    parser = PanOCRParser(data)
    with pytest.raises(
        PanOCRParserException,
        match=r"\[PanOCRParser\] No or invalid father name found in the PAN OCR API response.",
    ):
        parser.parse()


@pytest.mark.unittest
def test_pan_ocr_parser_missing_dob(load_json):
    """Test parser with missing DOB."""

    data = load_json("accounts/kyc/tests/fixtures/pan_ocr_success.json")
    del data["data"]["ocr_fields"][0]["dob"]
    parser = PanOCRParser(data)
    with pytest.raises(
        PanOCRParserException,
        match=r"\[PanOCRParser\] No or invalid DOB found in the PAN OCR API response.",
    ):
        parser.parse()
