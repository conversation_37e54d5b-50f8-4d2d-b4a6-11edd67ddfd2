from django.conf import settings
from django.urls import reverse

import pytest
from rest_framework import status

from accounts.billing_accounts.models import BillingAccountDocs, BillingAccounts
from accounts.kyc.cache_handler import AadhaarCacheHandler
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCSourceEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC
from accounts.kyc.tests.factories import KYCFactory


@pytest.fixture
def aadhaar_send_otp_url():
    return reverse("kyc:aadhaar-send-otp")


@pytest.fixture
def aadhaar_verify_otp_url():
    return reverse("kyc:aadhaar-verify-otp")


# ---- aadhaar send otp tests ----


@pytest.mark.parametrize(
    "kyc_status",
    [KYCStatusEnum.EXPIRED.value, KYCStatusEnum.FAILED.value, None],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_send_otp_success(
    client,
    aadhaar_send_otp_url,
    api_headers,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
    billing_account,
    mock_aadhaar_send_otp_api,
    kyc_status,
):
    """
    Test that the API returns a 200 status code and the correct response when the request is valid.
    Also test that the cache is set correctly.
    """

    if kyc_status:
        KYCFactory(
            billing_account=billing_account,
            mode=KYCModeEnum.AADHAAR.value,
            status=kyc_status,
        )

    mock_aadhaar_send_otp_api(
        expected_response={
            "data": {
                "client_id": valid_aadhaar_client_id,
                "otp_sent": True,
                "if_number": True,
                "valid_aadhaar": True,
                "status": "generate_otp_success",
            },
            "status_code": status.HTTP_200_OK,
            "message_code": "success",
            "message": "OTP Sent.",
            "success": True,
        },
        status_code=status.HTTP_200_OK,
    )
    response = client.post(
        aadhaar_send_otp_url,
        {"aadhaar_number": valid_aadhaar_number},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "status": "success",
        "code": status.HTTP_200_OK,
        "message": "OTP sent successfully on the phone number linked to Aadhaar number.",
        "data": None,
    }
    # assert cache data
    assert AadhaarCacheHandler(key=valid_aadhaar_number).fetch_otp_data() == {
        "verification_attempts": "0",
        "client_id": valid_aadhaar_client_id,
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_send_otp_api_requires_valid_billing_account_id_in_header(
    client, aadhaar_send_otp_url, valid_aadhaar_number
):
    """
    Test that the API requires a valid billing account ID in the header.
    """

    response = client.post(
        aadhaar_send_otp_url,
        {"aadhaar_number": valid_aadhaar_number},
        content_type="application/json",
    )
    # assert API response
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {
        "status": "error",
        "code": "permission_denied",
        "message": "You do not have permission to perform this action.",
        "errors": {"detail": "Invalid billing_account_id in headers!"},
    }


@pytest.mark.parametrize(
    "request_data",
    [
        {},
        {"aadhaar_number": None},
        {"aadhaar_number": ""},
        {"aadhaar_number": "123"},
        {"aadhaar_number": "invalid_aadhaar"},
        {"aadhaar_number": "************"},
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_send_otp_with_invalid_aadhaar_number_returns_http_400_bad_request(
    client, aadhaar_send_otp_url, api_headers, billing_account, request_data
):
    """
    Test that the API returns a 400 status code and the correct response when the request is invalid.
    """

    response = client.post(
        aadhaar_send_otp_url,
        request_data,
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_400_BAD_REQUEST,
        "message": "Invalid Aadhar format. Please enter a valid 12-digit Aadhaar number.",
        "errors": {"detail": "Invalid Aadhar format."},
    }


@pytest.mark.parametrize(
    "kyc_status",
    [
        KYCStatusEnum.PENDING.value,
        KYCStatusEnum.COMPLETED.value,
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_send_otp_with_active_kyc_returns_http_409_conflict(
    client,
    aadhaar_send_otp_url,
    api_headers,
    valid_aadhaar_number,
    billing_account,
    kyc_status,
):
    """Test that send OTP endpoint fails when there is an existing active KYC."""

    # Create an active KYC
    KYCFactory(
        billing_account=billing_account,
        mode=KYCModeEnum.AADHAAR.value,
        status=kyc_status,
    )

    response = client.post(
        aadhaar_send_otp_url,
        {"aadhaar_number": valid_aadhaar_number},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_409_CONFLICT
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_409_CONFLICT,
        "message": "KYC is already in progress or completed",
        "errors": {"detail": "KYC is already in progress or completed"},
    }


@pytest.mark.parametrize(
    "request_data",
    [
        {
            "data": None,
            "status_code": status.HTTP_400_BAD_REQUEST,
            "message_code": None,
            "message": "VID Number Not Supported.",
            "success": False,
        },
        {
            "data": {
                "client_id": "aadhaar_v2_bUnxJFrpkvjlkDgvayjv",
                "otp_sent": False,
                "if_number": False,
                "valid_aadhaar": False,
                "status": "invalid_aadhaar",
            },
            "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "message_code": "verification_failed",
            "message": "Verification Failed.",
            "success": False,
        },
        {
            "data": {
                "client_id": "aadhaar_v2_bUnxJFrpkvjlkDgvayjv",
                "otp_sent": False,
                "if_number": False,
                "valid_aadhaar": True,
                "status": "number_not_linked",  # hypothetical
            },
            "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "message_code": "verification_failed",
            "message": "Verification Failed.",
            "success": False,
        },
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_send_otp_for_external_api_failure_returns_http_424(
    client,
    aadhaar_send_otp_url,
    api_headers,
    valid_aadhaar_number,
    billing_account,
    mock_aadhaar_send_otp_api,
    request_data,
):
    """
    Test that the API returns a 424 status code and the correct response when the external API fails.
    """

    mock_aadhaar_send_otp_api(
        expected_response=request_data,
        status_code=request_data["status_code"],
    )
    response = client.post(
        aadhaar_send_otp_url,
        {"aadhaar_number": valid_aadhaar_number},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_424_FAILED_DEPENDENCY
    response_json = response.json()
    assert response_json["status"] == "error"
    assert response_json["code"] == status.HTTP_424_FAILED_DEPENDENCY
    assert response_json["message"]
    assert response_json["errors"]["detail"]
    # assert cache data
    assert not AadhaarCacheHandler(key=valid_aadhaar_number).fetch_otp_data()


@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_send_otp_for_limit_exceeded_returns_http_429(
    client,
    aadhaar_send_otp_url,
    api_headers,
    valid_aadhaar_number,
    billing_account,
    mock_aadhaar_send_otp_api,
):
    """
    Test that the API returns a 429 status code and the correct response when the limit is exceeded.
    """

    mock_aadhaar_send_otp_api(
        expected_response={
            "data": None,
            "status_code": status.HTTP_429_TOO_MANY_REQUESTS,
            "message_code": None,
            "message": "Rate Limited.",
            "success": False,
        },
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
    )
    response = client.post(
        aadhaar_send_otp_url,
        {"aadhaar_number": valid_aadhaar_number},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_429_TOO_MANY_REQUESTS,
        "message": "Maximum attempts reached. Please try again later or use GST-based authentication.",
        "errors": {
            "detail": "Maximum attempts reached. Please try again later or use GST-based authentication."
        },
    }
    # assert cache data
    assert not AadhaarCacheHandler(key=valid_aadhaar_number).fetch_otp_data()


# ---- aadhaar verify otp tests ----


@pytest.mark.parametrize(
    "kyc_status",
    [KYCStatusEnum.EXPIRED.value, KYCStatusEnum.FAILED.value, None],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_success(
    client,
    aadhaar_verify_otp_url,
    api_headers,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
    valid_aadhaar_otp,
    billing_account,
    load_json,
    mock_aadhaar_verify_otp_api,
    mock_aadhaar_download_pdf_api,
    mock_s3_bucket_download,
    mock_s3_bucket,
    aadhaar_doc_type,
    kyc_status,
):

    """Test that the API returns a 200 status code and the correct response when the request is valid.
    Also test that the cache is set correctly.
    """

    if kyc_status:
        KYCFactory(
            billing_account=billing_account,
            mode=KYCModeEnum.AADHAAR.value,
            status=kyc_status,
        )

    # setup cache data
    aadhaar_cache_handler = AadhaarCacheHandler(key=valid_aadhaar_number)
    aadhaar_cache_handler.save_otp_data(
        aadhaar_client_id=valid_aadhaar_client_id
    )
    # assert cache data
    assert aadhaar_cache_handler.fetch_otp_data() == {
        "verification_attempts": "0",
        "client_id": valid_aadhaar_client_id,
    }
    # load aadhaar verify otp api success response data
    aadhaar_data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_200_success_response.json"
    )
    # mock aadhaar verify otp api
    mock_aadhaar_verify_otp_api(
        expected_response=aadhaar_data,
        status_code=status.HTTP_200_OK,
    )
    # mock aadhaar download pdf api
    mocked_aadhaar_download_pdf_api = mock_aadhaar_download_pdf_api()
    # mock s3 download api
    mocked_s3_bucket_download = mock_s3_bucket_download(
        url=load_json(
            "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
        )["data"]["aadhaar_pdf"]
    )
    response = client.post(
        aadhaar_verify_otp_url,
        {"aadhaar_number": valid_aadhaar_number, "otp": valid_aadhaar_otp},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()
    assert json_response["data"]["task_id"] is not None
    assert json_response["status"] == "success"
    assert json_response["code"] == status.HTTP_200_OK
    assert json_response["message"] == "OTP verified successfully."

    # assert API calls
    assert mocked_aadhaar_download_pdf_api.call_count == 1
    assert mocked_s3_bucket_download.call_count == 1
    # assert Database data
    billing_account = BillingAccounts.objects.first()
    assert billing_account is not None
    kyc_qs = KYC.objects.filter(
        billing_account=billing_account,
        current_state=KYCStateEnum.VERIFICATION.value,
        mode=KYCModeEnum.AADHAAR.value,
        source=KYCSourceEnum.MYOPERATOR.value,
        status=KYCStatusEnum.PENDING.value,
    )
    assert kyc_qs.count() == 1
    kyc: KYC = kyc_qs.first()  # type: ignore
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.VERIFICATION.value  # type: ignore
    assert state.status == KYCStateStatusEnum.COMPLETED.value  # type: ignore
    assert state.data == aadhaar_data["data"]  # type: ignore
    assert state.task_id == json_response["data"]["task_id"]
    assert state.failure_reason is None
    assert state.started_at
    assert state.completed_at

    doc_qs = BillingAccountDocs.objects.filter(
        kyc=kyc,
        doc_type=aadhaar_doc_type,
        doc_number=valid_aadhaar_number,
        doc_name="398720250611143358901-2025-06-11-************",
        doc_ext="pdf",
        status=True,
    )
    assert doc_qs.count() == 1
    doc: BillingAccountDocs = doc_qs.first()  # type: ignore
    # assert S3 data
    s3_obj_response = mock_s3_bucket.get_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=doc.doc_path,
    )
    assert s3_obj_response["Body"].read() == b"test content"
    # assert cache data
    # verification_attempts should be incremented by 1
    assert aadhaar_cache_handler.fetch_otp_data() == {
        "verification_attempts": "1",
        "client_id": valid_aadhaar_client_id,
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_api_requires_valid_billing_account_id_in_header(
    client, aadhaar_verify_otp_url, valid_aadhaar_number
):
    """
    Test that the API requires a valid billing account ID in the header.
    """

    response = client.post(
        aadhaar_verify_otp_url,
        {"aadhaar_number": valid_aadhaar_number},
        content_type="application/json",
    )
    # assert API response
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {
        "status": "error",
        "code": "permission_denied",
        "message": "You do not have permission to perform this action.",
        "errors": {"detail": "Invalid billing_account_id in headers!"},
    }


@pytest.mark.parametrize(
    "request_data",
    [
        {},
        {"aadhaar_number": None},
        {"aadhaar_number": ""},
        {"aadhaar_number": "123"},
        {"aadhaar_number": "invalid_aadhaar"},
        {"aadhaar_number": "************"},
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_with_invalid_aadhaar_number_returns_http_400_bad_request(
    client, aadhaar_verify_otp_url, api_headers, billing_account, request_data
):
    """Test that the API returns a 400 status code and the correct response when the request is invalid."""

    response = client.post(
        aadhaar_verify_otp_url,
        request_data,
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_400_BAD_REQUEST,
        "message": "Invalid Aadhar format. Please enter a valid 12-digit Aadhaar number.",
        "errors": {"detail": "Invalid Aadhar format."},
    }


@pytest.mark.parametrize(
    "request_data",
    [
        {"otp": None},
        {"otp": ""},
        {"otp": "1"},
        {"otp": "12"},
        {"otp": "123"},
        {"otp": "1234"},
        {"otp": "12345"},
        {"otp": "1234567"},
        {"otp": "invalid_otp"},
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_with_invalid_aadhaar_otp_returns_http_400_bad_request(
    client,
    aadhaar_verify_otp_url,
    api_headers,
    valid_aadhaar_number,
    billing_account,
    request_data,
):
    """Test that the API returns a 400 status code and the correct response when the request is invalid."""

    response = client.post(
        aadhaar_verify_otp_url,
        {"aadhaar_number": valid_aadhaar_number, **request_data},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_400_BAD_REQUEST,
        "message": "Invalid Aadhaar number OTP. Please enter a valid Aadhaar number OTP.",
        "errors": {"detail": "Invalid Aadhaar number OTP."},
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_without_aadhaar_otp_sent_returns_http_400_bad_request(
    client,
    aadhaar_verify_otp_url,
    api_headers,
    valid_aadhaar_number,
    valid_aadhaar_otp,
):
    """Test that the API returns a 404 status code and the correct response when the request is invalid."""

    response = client.post(
        aadhaar_verify_otp_url,
        {"aadhaar_number": valid_aadhaar_number, "otp": valid_aadhaar_otp},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_400_BAD_REQUEST,
        "message": "OTP expired. Please request a new OTP.",
        "errors": {"detail": "OTP data not found or expired."},
    }


@pytest.mark.parametrize(
    "kyc_status",
    [
        KYCStatusEnum.PENDING.value,
        KYCStatusEnum.COMPLETED.value,
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_with_active_kyc_returns_http_409_conflict(
    client,
    aadhaar_verify_otp_url,
    api_headers,
    valid_aadhaar_number,
    valid_aadhaar_otp,
    billing_account,
    kyc_status,
):
    """Test that verify OTP endpoint fails when there is an existing active KYC."""

    # Create an active KYC
    KYCFactory(
        billing_account=billing_account,
        mode=KYCModeEnum.AADHAAR.value,
        status=kyc_status,
    )

    response = client.post(
        aadhaar_verify_otp_url,
        {"aadhaar_number": valid_aadhaar_number, "otp": valid_aadhaar_otp},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_409_CONFLICT
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_409_CONFLICT,
        "message": "KYC is already in progress or completed",
        "errors": {"detail": "KYC is already in progress or completed"},
    }


@pytest.mark.parametrize(
    "aadhaar_verify_otp_external_api_response_data",
    [
        {
            "test_data_file_path": "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_400_failure_response.json",
            "status_code": status.HTTP_400_BAD_REQUEST,
        },
        {
            "test_data_file_path": "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_422_failure_response.json",
            "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
        },
        {
            "test_data_file_path": "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_404_failure_response.json",
            "status_code": status.HTTP_404_NOT_FOUND,
        },
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_for_external_api_failure_returns_http_424(
    client,
    aadhaar_verify_otp_url,
    api_headers,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
    valid_aadhaar_otp,
    billing_account,
    load_json,
    mock_aadhaar_verify_otp_api,
    aadhaar_verify_otp_external_api_response_data,
):
    """Test that the API returns a 424 status code and the correct response when the external API fails."""

    # setup cache data
    aadhaar_cache_handler = AadhaarCacheHandler(key=valid_aadhaar_number)
    aadhaar_cache_handler.save_otp_data(
        aadhaar_client_id=valid_aadhaar_client_id
    )
    # assert cache data
    assert aadhaar_cache_handler.fetch_otp_data() == {
        "verification_attempts": "0",
        "client_id": valid_aadhaar_client_id,
    }
    # load aadhaar verify otp api failure response data
    aadhaar_data = load_json(
        aadhaar_verify_otp_external_api_response_data["test_data_file_path"]
    )
    # mock aadhaar verify otp api
    mock_aadhaar_verify_otp_api(
        expected_response=aadhaar_data,
        status_code=aadhaar_verify_otp_external_api_response_data[
            "status_code"
        ],
    )
    response = client.post(
        aadhaar_verify_otp_url,
        {"aadhaar_number": valid_aadhaar_number, "otp": valid_aadhaar_otp},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_424_FAILED_DEPENDENCY
    json_response = response.json()
    assert json_response["status"] == "error"
    assert json_response["code"] == status.HTTP_424_FAILED_DEPENDENCY
    assert json_response["message"]
    # assert Database data
    billing_account = BillingAccounts.objects.first()
    assert billing_account is not None
    assert KYC.objects.count() == 0
    # assert no docs are created
    assert BillingAccountDocs.objects.count() == 0
    # assert cache data
    # verification_attempts should be incremented by 1
    assert aadhaar_cache_handler.fetch_otp_data() == {
        "verification_attempts": "1",
        "client_id": valid_aadhaar_client_id,
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_for_aadhaar_verify_otp_external_api_limit_exceeded_returns_http_429(
    client,
    aadhaar_verify_otp_url,
    api_headers,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
    valid_aadhaar_otp,
    billing_account,
    mock_aadhaar_verify_otp_api,
):
    """Test that the API returns a 429 status code and the correct response when the limit is exceeded."""

    # setup cache data
    aadhaar_cache_handler = AadhaarCacheHandler(key=valid_aadhaar_number)
    aadhaar_cache_handler.save_otp_data(
        aadhaar_client_id=valid_aadhaar_client_id
    )
    # assert cache data
    assert aadhaar_cache_handler.fetch_otp_data() == {
        "verification_attempts": "0",
        "client_id": valid_aadhaar_client_id,
    }
    # mock aadhaar verify otp api
    mock_aadhaar_verify_otp_api(
        expected_response={
            "data": None,
            "status_code": status.HTTP_429_TOO_MANY_REQUESTS,
            "message_code": None,
            "message": "Rate Limited.",
            "success": False,
        },
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
    )
    response = client.post(
        aadhaar_verify_otp_url,
        {"aadhaar_number": valid_aadhaar_number, "otp": valid_aadhaar_otp},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_429_TOO_MANY_REQUESTS,
        "message": "Maximum attempts reached. Please try again later or use GST-based authentication.",
        "errors": {
            "detail": "Maximum attempts reached. Please try again later or use GST-based authentication."
        },
    }
    # assert Database data
    billing_account = BillingAccounts.objects.first()
    assert billing_account is not None
    assert KYC.objects.count() == 0
    # assert no docs are created
    assert BillingAccountDocs.objects.count() == 0
    # assert cache data
    # verification_attempts should be incremented by 1
    assert aadhaar_cache_handler.fetch_otp_data() == {
        "verification_attempts": "1",
        "client_id": valid_aadhaar_client_id,
    }


@pytest.mark.django_db
@pytest.mark.unittest
def test_aadhaar_verify_otp_for_verification_attempts_exceeded_returns_http_429(
    client,
    aadhaar_verify_otp_url,
    api_headers,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
    valid_aadhaar_otp,
    billing_account,
):
    """Test that the API returns a 429 status code and the correct response when the verification attempts are exceeded."""

    # setup cache data
    aadhaar_cache_handler = AadhaarCacheHandler(key=valid_aadhaar_number)
    aadhaar_cache_handler.save_otp_data(
        aadhaar_client_id=valid_aadhaar_client_id
    )
    for _ in range(aadhaar_cache_handler.AADHAAR_OTP_VERIFICATION_ATTEMPTS):
        aadhaar_cache_handler.incr_verification_attempts()

    # assert cache data
    assert aadhaar_cache_handler.fetch_otp_data() == {
        "verification_attempts": str(
            aadhaar_cache_handler.AADHAAR_OTP_VERIFICATION_ATTEMPTS
        ),
        "client_id": valid_aadhaar_client_id,
    }
    response = client.post(
        aadhaar_verify_otp_url,
        {"aadhaar_number": valid_aadhaar_number, "otp": valid_aadhaar_otp},
        content_type="application/json",
        **api_headers,
    )
    # assert API response
    assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_429_TOO_MANY_REQUESTS,
        "message": "Maximum attempts reached. Please try again later or use GST-based authentication.",
        "errors": {
            "detail": "Maximum attempts reached. Please try again later or use GST-based authentication."
        },
    }
    # assert Database data
    billing_account = BillingAccounts.objects.first()
    assert billing_account is not None
    assert KYC.objects.count() == 0
    # assert no docs are created
    assert BillingAccountDocs.objects.count() == 0
