from dataclasses import asdict

from django.conf import settings

import pytest
from rest_framework import status

from accounts.billing_accounts.models import BillingAccountDocs
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.exceptions import (
    KycFailedException,
    KycNotFoundException,
    PanOcrApiException,
)
from accounts.kyc.models import KYC
from accounts.kyc.parsers import PanOCRParser
from accounts.kyc.tasks import fetch_pan_info_from_file_task


@pytest.mark.parametrize("extension", [".png", ".jpg", ".jpeg"])
@pytest.mark.django_db
@pytest.mark.unittest
def test_fetch_pan_info_from_file_task_success(
    valid_pan_file_in_s3_tmp,
    create_kyc_for_state,
    load_json,
    pan_doc_type,
    mock_s3_bucket,
    mock_surepass_fetch_pan_info_from_file_api,
    extension,
):
    """Test fetch pan info from file task.

    This test verifies that:
    1. Task executes successfully
    2. KYC state status is updated to completed
    3. Document is created with correct details
    4. File is uploaded to S3
    """

    # Create KYC in verification state
    kyc: KYC = create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.VERIFICATION,
        kyc_status=KYCStatusEnum.COMPLETED,
        state_data={"data": "data"},
    )
    create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.UPLOAD_PAN,
        state_status=KYCStateStatusEnum.PENDING,
        kyc=kyc,
    )
    s3_key = valid_pan_file_in_s3_tmp(extension)
    # upload file to S3
    mock_s3_bucket.put_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=s3_key,
        Body=b"test content",
    )
    # mock fetch pan info from file API response
    pan_data = load_json(
        "accounts/kyc/tests/fixtures/pan_ocr_200_success_response.json"
    )
    pan_number = pan_data["data"]["ocr_fields"][0]["pan_number"]["value"]
    mocked_surepass_fetch_pan_info_from_file_api = (
        mock_surepass_fetch_pan_info_from_file_api(
            expected_response=pan_data, status_code=status.HTTP_200_OK
        )
    )
    # Execute task
    task = fetch_pan_info_from_file_task.apply_async(args=[s3_key, kyc.id])  # type: ignore
    assert task.successful()
    # assert API calls
    assert mocked_surepass_fetch_pan_info_from_file_api.call_count == 1
    # assert database
    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.UPLOAD_PAN.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.UPLOAD_PAN.value
    assert state.status == KYCStateStatusEnum.COMPLETED.value
    assert state.data == asdict(PanOCRParser(pan_data).parse())
    assert state.failure_reason is None
    assert state.started_at
    assert state.completed_at
    doc_qs = BillingAccountDocs.objects.filter(
        kyc=kyc,
        doc_type=pan_doc_type,
        doc_number=pan_number,
        doc_name=pan_number,
        doc_ext=extension.split(".")[-1],
        status=True,
    )
    assert doc_qs.count() == 1
    # assert S3 object is copied to new location
    # from <bucket>/tmp/companydoc/ to <bucket>/companydoc/
    doc = doc_qs.first()
    response = mock_s3_bucket.get_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=doc.doc_path
    )
    assert response["Body"].read() == b"test content"


@pytest.mark.parametrize("extension", [".png", ".jpg", ".jpeg"])
@pytest.mark.django_db
@pytest.mark.unittest
def test_fetch_pan_info_from_file_task_kyc_not_found(
    valid_pan_file_in_s3_tmp,
    extension,
):
    """Test fetch pan info from file task when KYC object is not found.

    This test verifies that:
    1. Task handles non-existent KYC gracefully
    2. Appropriate exception is raised
    """

    s3_key = valid_pan_file_in_s3_tmp(extension)
    kyc_id = "1"
    # Execute task
    with pytest.raises(KycNotFoundException):
        task = fetch_pan_info_from_file_task.apply_async(args=[s3_key, kyc_id])  # type: ignore
        assert not task.successful()


@pytest.mark.parametrize(
    "extension",
    [".png", ".jpg", ".jpeg"],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_fetch_pan_info_from_file_task_with_fetch_pan_info_from_file_api_failure(
    valid_pan_file_in_s3_tmp,
    create_kyc_for_state,
    load_json,
    pan_doc_type,
    mock_s3_bucket,
    mock_surepass_fetch_pan_info_from_file_api,
    extension,
):
    """Test fetch pan info from file task when fetch pan info from file API fails.

    This test verifies that:
    1. Task handles API failure gracefully
    2. KYC state status is updated to failed
    3. Failure reason is logged and stored in Database
    4. No Document is created
    """

    # Create KYC in verification state
    kyc: KYC = create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.VERIFICATION,
        kyc_status=KYCStatusEnum.COMPLETED,
        state_data={"data": "data"},
    )
    create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.UPLOAD_PAN,
        state_status=KYCStateStatusEnum.PENDING,
        kyc=kyc,
    )
    s3_key = valid_pan_file_in_s3_tmp(extension)
    # upload file to S3
    mock_s3_bucket.put_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=s3_key,
        Body=b"test content",
    )
    # mock fetch pan info from file API response
    pan_ocr_error_response = load_json(
        "accounts/kyc/tests/fixtures/pan_ocr_422_error_response.json"
    )
    mocked_surepass_fetch_pan_info_from_file_api = (
        mock_surepass_fetch_pan_info_from_file_api(
            expected_response=pan_ocr_error_response,
            status_code=status.HTTP_424_FAILED_DEPENDENCY,
        )
    )
    # Execute task and expect exception
    with pytest.raises(PanOcrApiException):
        task = fetch_pan_info_from_file_task.apply_async(args=[s3_key, kyc.id])  # type: ignore
        assert not task.successful()
    # assert API calls
    assert mocked_surepass_fetch_pan_info_from_file_api.call_count == 1
    # assert database
    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.UPLOAD_PAN.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.UPLOAD_PAN.value
    assert state.status == KYCStateStatusEnum.FAILED.value
    assert state.data == {}
    assert state.failure_reason == kyc.generate_failure_reason(
        reason="Failed to fetch PAN info from file using Surepass PAN OCR API.",
        exception_name=PanOcrApiException.__name__,
    )
    assert state.started_at
    assert not state.completed_at
    doc_qs = BillingAccountDocs.objects.filter(
        kyc=kyc,
        doc_type=pan_doc_type,
        status=True,
    )
    assert doc_qs.count() == 0


@pytest.mark.parametrize("extension", [".png", ".jpg", ".jpeg"])
@pytest.mark.django_db
@pytest.mark.unittest
def test_fetch_pan_info_from_file_task_download_file_from_s3_failure(
    valid_pan_file_in_s3_tmp,
    create_kyc_for_state,
    pan_doc_type,
    mock_s3_bucket,
    extension,
):
    """Test fetch pan info from file task when file download from S3 fails.

    This test verifies that:
    1. Task handles S3 download failure gracefully
    2. KYC state status is updated to failed
    3. Failure reason is logged and stored in Database
    4. No Document is created
    """

    # Create KYC in verification state
    kyc: KYC = create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.VERIFICATION,
        kyc_status=KYCStatusEnum.COMPLETED,
        state_data={"data": "data"},
    )
    create_kyc_for_state(
        kyc_mode=KYCModeEnum.AADHAAR,
        kyc_state=KYCStateEnum.UPLOAD_PAN,
        state_status=KYCStateStatusEnum.PENDING,
        kyc=kyc,
    )
    s3_key = valid_pan_file_in_s3_tmp(extension)
    # Execute task and expect exception
    with pytest.raises(KycFailedException, match="S3 file download failed."):
        task = fetch_pan_info_from_file_task.apply_async(args=[s3_key, kyc.id])  # type: ignore
        assert not task.successful()
    # assert database
    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.UPLOAD_PAN.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.UPLOAD_PAN.value
    assert state.status == KYCStateStatusEnum.FAILED.value
    assert state.data == {}
    assert state.failure_reason == kyc.generate_failure_reason(
        reason="S3 file download failed.",
        exception_name=KycFailedException.__name__,
    )
    assert state.started_at
    assert not state.completed_at
    doc_qs = BillingAccountDocs.objects.filter(
        kyc=kyc,
        doc_type=pan_doc_type,
        status=True,
    )
    assert doc_qs.count() == 0
