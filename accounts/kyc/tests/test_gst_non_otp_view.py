from django.conf import settings
from django.urls import reverse

import pytest
from rest_framework import status

from accounts.billing_accounts.models import BillingAccountDocs
from accounts.kyc.cache_handler import GstCacheHandler
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.tests.factories import KYCFactory


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_non_otp_success(
    client,
    valid_gst_number,
    mock_gst_detail_api_with_custom_data,
    api_headers,
    mock_surepass_api_gst_pdf,
    mock_s3_bucket_download,
    mock_s3_bucket,
    load_json,
    gst_doc_type,
    billing_account,
):
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.AADHAAR.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    url = reverse("kyc:gst_non_otp", kwargs={"kyc_id": kyc.id})
    kyc.create_kyc_state(data={"gst_number": valid_gst_number}, task_id="123")
    kyc.mark_latest_state_as_completed()

    gst_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")
    # Setup API response
    gst_detail_api_res = mock_gst_detail_api_with_custom_data(
        mobile_number="**********",
        gst_number=valid_gst_number,
        expected_res=gst_data,
    )

    gst_pdf_api_res = mock_surepass_api_gst_pdf()
    s3_download_api_res = mock_s3_bucket_download(
        url=load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")[
            "data"
        ]["pdf_report"]
    )

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )

    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()
    assert json_response["data"]["task_id"] is not None
    assert json_response["status"] == "success"
    assert json_response["code"] == status.HTTP_200_OK
    assert (
        json_response["message"] == "GST number without OTP saved successfully."
    )

    assert gst_detail_api_res.call_count == 1
    assert gst_pdf_api_res.call_count == 1
    assert s3_download_api_res.call_count == 1

    cache = GstCacheHandler(valid_gst_number)
    assert cache.get_gst_data() == gst_data["data"]
    assert cache.get(cache.non_otp_attempt_key) == 1

    # Verify KYC state transition
    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.GST_INFO.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.GST_INFO.value
    assert (
        state.status == KYCStateStatusEnum.COMPLETED.value
    )  # Tasks completed synchronously
    assert state.task_id == json_response["data"]["task_id"]
    assert state.failure_reason is None

    # Verify document creation
    doc_qs = BillingAccountDocs.objects.filter(
        kyc=kyc,
        doc_type=gst_doc_type,
        doc_number=valid_gst_number,
        doc_name="pdf_report_gstin_1749201959545362",
        doc_ext="pdf",
        status=True,
    )
    assert doc_qs.count() == 1
    doc: BillingAccountDocs = doc_qs.first()  # type: ignore

    s3_obj_response = mock_s3_bucket.get_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=doc.doc_path,
    )
    assert s3_obj_response["Body"].read() == b"test content"


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize(
    "request_data,expected_message, expected_error,expected_code",
    [
        (
            {"gst_number": "invalid_gst"},
            "Invalid GST number. Please enter a correct GST or choose PAN KYC.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
        (
            {"gst_number": ""},
            "Invalid GST number. Please enter a correct GST or choose PAN KYC.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
        (
            {"gst_number": None},
            "Invalid GST number. Please enter a correct GST or choose PAN KYC.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
        (
            {},
            "Invalid GST number. Please enter a correct GST or choose PAN KYC.",
            "Invalid GST number.",
            status.HTTP_400_BAD_REQUEST,
        ),
    ],
)
def test_gst_non_otp_invalid_input(
    client,
    request_data,
    expected_message,
    expected_error,
    expected_code,
    api_headers,
    billing_account,
    valid_gst_number,
):
    # Create initial KYC
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.AADHAAR.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    url = reverse("kyc:gst_non_otp", kwargs={"kyc_id": kyc.id})

    kyc.create_kyc_state(data={"gst_number": valid_gst_number}, task_id="123")

    response = client.post(
        url,
        request_data,
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {
        "code": expected_code,
        "errors": {"detail": expected_error},
        "message": expected_message,
        "status": "error",
    }
    cache = GstCacheHandler(valid_gst_number)
    assert cache.get_gst_data() is None
    assert cache.get(cache.non_otp_attempt_key) is None
    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.VERIFICATION.value
    assert state.status == KYCStateStatusEnum.PENDING.value
    assert state.task_id == "123"
    assert state.failure_reason is None


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_non_otp_kyc_not_found(
    client,
    valid_gst_number,
    api_headers,
):
    url = reverse("kyc:gst_non_otp", kwargs={"kyc_id": "123"})

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_404_NOT_FOUND,
        "message": "KYC not found.",
        "errors": {"detail": "KYC not found."},
    }
    cache = GstCacheHandler(valid_gst_number)
    assert cache.get_gst_data() is None
    assert cache.get(cache.non_otp_attempt_key) is None


@pytest.mark.django_db
@pytest.mark.unittest
def test_gst_non_otp_max_attempts_exceeded(
    client,
    valid_gst_number,
    api_headers,
    billing_account,
):
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.AADHAAR.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    kyc.create_kyc_state(data={"gst_number": valid_gst_number}, task_id="123")
    kyc.mark_latest_state_as_completed()
    url = reverse("kyc:gst_non_otp", kwargs={"kyc_id": kyc.id})

    cache = GstCacheHandler(valid_gst_number)
    for _ in range(cache.NON_OTP_ATTEMPTS):
        cache.incr_non_otp_attempts()

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_429_TOO_MANY_REQUESTS,
        "message": "Maximum attempts exceeded. Please try again after some time.",
        "errors": {
            "detail": "Maximum attempts exceeded. Please try again after some time."
        },
    }

    cache = GstCacheHandler(valid_gst_number)
    assert cache.get_gst_data() is None
    assert cache.get(cache.non_otp_attempt_key) == cache.NON_OTP_ATTEMPTS

    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.VERIFICATION.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.VERIFICATION.value
    assert state.status == KYCStateStatusEnum.COMPLETED.value
    assert state.task_id == "123"
    assert state.failure_reason is None


@pytest.mark.django_db
@pytest.mark.unittest
def test_transition_to_gst_info_cond(
    client,
    valid_gst_number,
    api_headers,
    billing_account,
):
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.AADHAAR.value,
        current_state=KYCStateEnum.GST_INFO.value,
    )
    kyc.create_kyc_state(data={"gst_number": valid_gst_number}, task_id="123")
    url = reverse("kyc:gst_non_otp", kwargs={"kyc_id": kyc.id})

    response = client.post(
        url,
        {"gst_number": valid_gst_number},
        content_type="application/json",
        **api_headers,
    )
    assert response.status_code == status.HTTP_412_PRECONDITION_FAILED
    assert response.json() == {
        "status": "error",
        "code": status.HTTP_412_PRECONDITION_FAILED,
        "message": "Not allowed to transition from current state.",
        "errors": {"detail": "Not allowed to transition from current state."},
    }
    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.GST_INFO.value
    assert kyc.status == KYCStatusEnum.PENDING.value
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.GST_INFO.value
    assert state.status == KYCStateStatusEnum.PENDING.value
    assert state.task_id == "123"
    assert state.failure_reason is None
