import pytest

from accounts.billing_accounts.utils.gst_parser import <PERSON>pass<PERSON><PERSON>ars<PERSON>
from accounts.exceptions import OTPMaxAttemptException
from accounts.kyc.cache_handler import (
    AadhaarCache<PERSON>andler,
    GstCache<PERSON>andler,
    PanCacheHandler,
)
from accounts.kyc.exceptions import (
    OtpDataNotFoundException,
    PanUploadAttemptsLimitExceededException,
)


@pytest.mark.unittest
def test_gst_cache_handler_incr_attempt():
    """Test incrementing send attempts."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)
    handler.incr_send_attempt()
    assert handler.get(handler.send_attempt_key) == 1


@pytest.mark.unittest
def test_gst_cache_handler_send_attempt_allowed():
    """Test send attempt limit validation."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)
    phone_number = "+************"
    otp = "1234"

    # Set initial data
    handler.set_otp_data(phone_number, otp, "0")

    # Test within limit
    for _ in range(handler.MAX_SEND_ATTEMPTS - 1):
        handler.incr_send_attempt()
    assert handler.send_attempt_allowed() is True

    # Test exceeding limit
    handler.incr_send_attempt()
    assert handler.send_attempt_allowed() is False


@pytest.mark.unittest
def test_gst_cache_handler_set_non_otp_attempts(load_json):
    """Test setting non-OTP attempts."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)
    assert handler.get(handler.non_otp_attempt_key) is None
    for _ in range(handler.NON_OTP_ATTEMPTS):
        assert handler.non_otp_attempt_allowed()
        handler.incr_non_otp_attempts()

    assert not handler.non_otp_attempt_allowed()

    assert handler.get(handler.non_otp_attempt_key) == handler.NON_OTP_ATTEMPTS


@pytest.mark.unittest
def test_gst_cache_handler_get_and_set_otp_data():
    """Test setting OTP data."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)
    phone_number = "+************"
    otp = "1234"

    # Set OTP data
    handler.set_otp_data(phone_number, otp, "0")

    # Verify data
    data = handler.get_otp_data()
    assert data["phone_number"] == phone_number
    assert data["otp"] == otp
    assert data[handler.VERIFICATION_ATTEMPTS_FIELD] == "0"
    assert "verified" not in data


@pytest.mark.unittest
def test_gst_cache_handler_get_otp_data_not_found():
    """Test getting OTP data when not found."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)

    with pytest.raises(
        OtpDataNotFoundException, match="OTP expired. Please request a new OTP."
    ):
        handler.get_otp_data()


@pytest.mark.unittest
def test_gst_cache_handler_verify_attempt_allowed():
    """Test verification attempt limit validation."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)
    phone_number = "+************"
    otp = "1234"

    # Set initial data
    handler.set_otp_data(phone_number, otp, "0")

    # Test within limit
    for _ in range(handler.MAX_VERIFY_ATTEMPTS - 1):
        handler.incr_verification_attempt()
    assert handler.verify_attempt_allowed() is True

    # Test exceeding limit
    handler.incr_verification_attempt()
    assert handler.verify_attempt_allowed() is False


@pytest.mark.unittest
def test_gst_cache_handler_incr_verification_attempt():
    """Test incrementing verification attempts."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)
    phone_number = "+************"
    otp = "1234"

    # Set initial data
    handler.set_otp_data(phone_number, otp, "0")

    # Increment verification attempt
    handler.incr_verification_attempt()

    data = handler.get_otp_data()
    assert data[handler.VERIFICATION_ATTEMPTS_FIELD] == "1"


@pytest.mark.unittest
def test_gst_cache_handler_mark_as_verified():
    """Test marking OTP as verified."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)
    phone_number = "+************"
    otp = "1234"

    # Set initial data
    handler.set_otp_data(phone_number, otp, "0")

    # Mark as verified
    handler.mark_as_verified({"phone_number": phone_number, "otp": otp})

    data = handler.get_otp_data()
    assert data["verified"] == "true"


@pytest.mark.unittest
def test_gst_cache_handler_set_gst_data(load_json):
    """Test setting GST detail in cache."""

    gst_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"]
    gst_detail = SurepassGSTParser(gst_data).parse()

    handler = GstCacheHandler("test_key")
    handler.set_gst_data(gst_detail)

    assert handler.get_gst_data() == gst_data


@pytest.mark.unittest
def test_gst_cache_handler_get_verification_attempts():
    """Test getting verification attempts count."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstCacheHandler(gst_number)
    handler.set_otp_data("+************", "123456", "2")

    assert handler.get_verification_attempts() == "2"
    handler.incr_verification_attempt()
    assert handler.get_verification_attempts() == "3"


@pytest.mark.unittest
def test_aadhaar_cache_handler_save_and_fetch_otp_data(
    valid_aadhaar_number, valid_aadhaar_client_id
):
    """Test saving and fetching OTP data."""

    handler = AadhaarCacheHandler(key=valid_aadhaar_number)
    handler.save_otp_data(aadhaar_client_id=valid_aadhaar_client_id)

    data = handler.fetch_otp_data()
    assert data == {
        "verification_attempts": "0",
        "client_id": valid_aadhaar_client_id,
    }


@pytest.mark.unittest
def test_aadhaar_cache_handler_validate_verification_attempts(
    valid_aadhaar_number, valid_aadhaar_client_id
):
    """Test validating verification attempts."""

    handler = AadhaarCacheHandler(key=valid_aadhaar_number)
    handler.save_otp_data(aadhaar_client_id=valid_aadhaar_client_id)

    assert handler.fetch_otp_data() == {
        "verification_attempts": "0",
        "client_id": valid_aadhaar_client_id,
    }

    for _ in range(handler.AADHAAR_OTP_VERIFICATION_ATTEMPTS - 1):
        handler.incr_verification_attempts()

    assert handler.is_verification_attempt_allowed() is True
    assert handler.fetch_otp_data() == {
        "verification_attempts": "5",
        "client_id": valid_aadhaar_client_id,
    }

    handler.incr_verification_attempts()

    with pytest.raises(OTPMaxAttemptException):
        handler.validate_verification_attempts()

    assert handler.is_verification_attempt_allowed() is False
    assert handler.fetch_otp_data() == {
        "verification_attempts": "6",
        "client_id": valid_aadhaar_client_id,
    }


@pytest.mark.unittest
def test_aadhaar_cache_handler_incr_verification_attempts(
    valid_aadhaar_number, valid_aadhaar_client_id
):
    """Test incrementing verification attempts."""

    handler = AadhaarCacheHandler(key=valid_aadhaar_number)
    handler.save_otp_data(aadhaar_client_id=valid_aadhaar_client_id)

    data = handler.fetch_otp_data()
    assert data == {
        "verification_attempts": "0",
        "client_id": valid_aadhaar_client_id,
    }

    handler.incr_verification_attempts()
    data = handler.fetch_otp_data()
    # assert verification_attempts is incremented by 1
    assert data == {
        "verification_attempts": "1",
        "client_id": valid_aadhaar_client_id,
    }


@pytest.mark.unittest
def test_pan_cache_handler_is_pan_upload_attempt_allowed(valid_pan_number):
    """Test if pan upload attempt is allowed."""

    pan_cache_handler = PanCacheHandler(key=valid_pan_number)
    assert pan_cache_handler.is_pan_upload_attempt_allowed() is True

    for _ in range(pan_cache_handler.PAN_UPLOAD_ATTEMPTS_LIMIT):
        pan_cache_handler.incr_pan_upload_attempts()

    assert pan_cache_handler.is_pan_upload_attempt_allowed() is False


@pytest.mark.unittest
def test_pan_cache_handler_validate_pan_upload_attempts(valid_pan_number):
    """Test validating pan upload attempts."""

    pan_cache_handler = PanCacheHandler(key=valid_pan_number)
    assert pan_cache_handler.is_pan_upload_attempt_allowed() is True

    for _ in range(pan_cache_handler.PAN_UPLOAD_ATTEMPTS_LIMIT):
        pan_cache_handler.incr_pan_upload_attempts()

    with pytest.raises(PanUploadAttemptsLimitExceededException):
        pan_cache_handler.validate_pan_upload_attempts()


@pytest.mark.unittest
def test_pan_cache_handler_incr_pan_upload_attempts(valid_pan_number):
    """Test incrementing pan upload attempts."""

    pan_cache_handler = PanCacheHandler(key=valid_pan_number)
    assert pan_cache_handler.get() is None
    pan_cache_handler.incr_pan_upload_attempts()
    assert pan_cache_handler.get() == 1
