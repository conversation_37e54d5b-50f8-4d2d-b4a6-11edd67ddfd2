import logging
from typing import Any, Dict, Optional

from django.conf import settings

from accounts.billing_accounts.utils.gst_parser import GSTDetail
from accounts.exceptions import OTPMaxAttemptException
from accounts.kyc.exceptions import (
    OtpDataNotFoundException,
    PanUploadAttemptsLimitExceededException,
)
from accounts.utils.cache_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseOtpCacheHandler

logger = logging.getLogger(__name__)


class GstCacheHandler(BaseOtpCacheHandler):
    MAX_SEND_ATTEMPTS = settings.GST_LIMITS["MAX_SEND_ATTEMPTS"]
    MAX_VERIFY_ATTEMPTS = settings.GST_LIMITS["MAX_VERIFY_ATTEMPTS"]
    NON_OTP_ATTEMPTS = settings.GST_LIMITS["NON_OTP_ATTEMPTS"]
    ATTEMPT_TTL = settings.GST_LIMITS["ATTEMPT_TTL"]
    OTP_TTL = settings.GST_LIMITS["OTP_TTL"]
    GST_DATA_TTL = settings.GST_LIMITS["GST_DATA_TTL"]

    VERIFICATION_ATTEMPTS_FIELD = "verification_attempts"

    def __init__(self, gst_number: str):
        self.otp_key = f"kyc:gst:{gst_number}__otp"
        self.send_attempt_key = f"kyc:gst:{gst_number}__otp_attempts"
        self.gst_data_key = f"kyc:gst:{gst_number}"
        self.non_otp_attempt_key = f"kyc:gst:{gst_number}__non_otp_attempts"

    def get_max_send_attempts(self) -> int:
        return self.MAX_SEND_ATTEMPTS

    def get_max_verification_attempts(self) -> int:
        return self.MAX_VERIFY_ATTEMPTS

    def get_otp_data(self) -> Dict[str, Any]:
        otp_data = self.hgetall(self.otp_key)
        if not otp_data:
            logger.info(f"OTP data not found for {self.otp_key}")
            raise OtpDataNotFoundException()
        return otp_data

    def get_verification_attempts(self) -> Optional[str]:
        return self.hget(self.otp_key, self.VERIFICATION_ATTEMPTS_FIELD)

    def send_attempt_allowed(self) -> bool:
        no_of_attempts = self.get(self.send_attempt_key)
        return self.attempt_allowed(no_of_attempts, self.MAX_SEND_ATTEMPTS)

    def verify_attempt_allowed(self) -> bool:
        no_of_attempts = self.get_verification_attempts()
        if no_of_attempts is None:
            raise OtpDataNotFoundException()
        return self.attempt_allowed(
            int(no_of_attempts), self.MAX_VERIFY_ATTEMPTS
        )

    def non_otp_attempt_allowed(self):
        no_of_attempts = self.get(self.non_otp_attempt_key)
        return self.attempt_allowed(no_of_attempts, self.NON_OTP_ATTEMPTS)

    def incr_send_attempt(self, ttl=ATTEMPT_TTL) -> int:
        attempt = self.incr(self.send_attempt_key, timeout=ttl)
        logger.info(
            f"Send attempt incremented for {self.send_attempt_key} to {attempt} with ttl: {ttl}"
        )
        return attempt

    def incr_verification_attempt(self) -> int:
        result = self.hincrby(
            self.otp_key,
            self.VERIFICATION_ATTEMPTS_FIELD,
        )
        logger.info(
            f"Verification attempt incremented for {self.otp_key} to {result}"
        )
        return result

    def incr_non_otp_attempts(self) -> int:
        result = self.incr(self.non_otp_attempt_key)
        logger.info(
            f"Non OTP attempt incremented for {self.non_otp_attempt_key} to {result}"
        )
        return result

    def set_otp_data(
        self, phone_number: str, otp: str, verification_attempts: str = "0"
    ):
        data = {
            "phone_number": phone_number,
            "otp": otp,
            self.VERIFICATION_ATTEMPTS_FIELD: verification_attempts,
        }
        logger.info(
            f"Setting OTP data: {data} with key:{self.otp_key} for {self.OTP_TTL} seconds"
        )
        return self.hmset(self.otp_key, data, self.OTP_TTL)

    def mark_as_verified(self, data: Dict[str, Any]):
        logger.info(f"Marking OTP data as verified for {self.otp_key}.")
        self.hset(self.otp_key, "verified", "true")

    def set_gst_data(self, gst_data: GSTDetail, timeout=GST_DATA_TTL):
        logger.info(
            f"Setting GST data: {gst_data} with key:{self.gst_data_key}"
        )
        self.save(self.gst_data_key, gst_data.data, timeout=timeout)

    def get_gst_data(self):
        return self.get(self.gst_data_key)


class AadhaarCacheHandler(BaseCacheHandler):
    AADHAAR_OTP_TTL = settings.AADHAAR_LIMITS["OTP_TTL"]
    AADHAAR_OTP_VERIFICATION_ATTEMPTS = settings.AADHAAR_LIMITS[
        "OTP_VERIFICATION_ATTEMPTS"
    ]

    def __init__(self, key: str):
        super().__init__(key=f"kyc:aadhaar:{key}")

    def save_otp_data(
        self, aadhaar_client_id: str, timeout: int = AADHAAR_OTP_TTL
    ):
        """Saves the OTP data in cache.

        Args:
            aadhaar_client_id (str): The client id returned by the send OTP API.
            timeout (int, optional): The timeout for the cache in seconds. Defaults to AADHAAR_OTP_TTL.
        """

        data = {"verification_attempts": 0, "client_id": aadhaar_client_id}
        logger.info(
            "[AadhaarCacheHandler] Setting Aadhaar key: %s, data: %s for %s seconds."
            % (self._key, data, timeout)
        )
        self.hmset(mapping=data, timeout=timeout)

    def otp_data_exists(self) -> bool:
        """Checks if the OTP data exists in cache.

        Returns:
            bool: If the OTP data exists in cache.
        """

        return self.exists()

    def fetch_otp_data(self) -> dict:
        """Fetches the OTP data from cache.

        Returns:
            dict: The OTP data from cache.
        """

        return self.hgetall()

    def is_verification_attempt_allowed(self) -> bool:
        """Checks if the verification attempt is allowed.

        Returns:
            bool: If the verification attempt is allowed.
        """

        cache_verification_attempts = int(
            self.fetch_otp_data().get("verification_attempts", 0)
        )
        return (
            cache_verification_attempts < self.AADHAAR_OTP_VERIFICATION_ATTEMPTS
        )

    def validate_verification_attempts(self):
        """Validates the verification attempts.

        Raises:
            OTPMaxAttemptException: If the verification attempts are not allowed.
        """

        if not self.is_verification_attempt_allowed():
            error_msg = (
                "OTP verification attempts exceeded. Maximum allowed: %s"
                % self.AADHAAR_OTP_VERIFICATION_ATTEMPTS
            )
            logger.error("[AadhaarCacheHandler] %s" % error_msg)
            raise OTPMaxAttemptException(
                "Maximum attempts reached. Please try again later or use GST-based authentication."
            )

    def incr_verification_attempts(
        self, amount: int = 1, timeout: int = AADHAAR_OTP_TTL
    ) -> int:
        """Increments the verification attempts.

        Args:
            amount (int, optional): The amount to increment by. Defaults to 1.
            timeout (int, optional): The timeout for the cache in seconds. Defaults to AADHAAR_OTP_TTL.

        Returns:
            int: The new verification attempts count.
        """

        logger.info(
            "[AadhaarCacheHandler] Incrementing verification_attempts by %s for key: %s with timeout: %s seconds."
            % (amount, self._key, timeout)
        )
        return self.hincrby(
            "verification_attempts", amount=amount, timeout=timeout
        )


class PanCacheHandler(BaseCacheHandler):
    PAN_UPLOAD_ATTEMPTS_CACHE_TTL_IN_SECONDS = settings.PAN[
        "PAN_UPLOAD_ATTEMPTS_CACHE_TTL_IN_SECONDS"
    ]
    PAN_UPLOAD_ATTEMPTS_LIMIT = settings.PAN["PAN_UPLOAD_ATTEMPTS_LIMIT"]

    def __init__(self, key: str):
        super().__init__(key=f"kyc:pan:{key}__pan_upload_attempts")

    def is_pan_upload_attempt_allowed(self) -> bool:
        """Checks if the pan upload attempt is allowed.

        Returns:
            bool: If the pan upload attempt is allowed.
        """

        cache_pan_upload_attempts = int(self.get() or 0)
        return cache_pan_upload_attempts < self.PAN_UPLOAD_ATTEMPTS_LIMIT

    def validate_pan_upload_attempts(self):
        """Validates the pan upload attempts.

        Raises:
            PanUploadAttemptsLimitException: If the pan upload attempts are not allowed.
        """

        if not self.is_pan_upload_attempt_allowed():
            error_msg = (
                "PAN upload attempts exceeded. Maximum allowed: %s"
                % self.PAN_UPLOAD_ATTEMPTS_LIMIT
            )
            logger.error("[PanCacheHandler] %s" % error_msg)
            raise PanUploadAttemptsLimitExceededException(error_msg)

    def incr_pan_upload_attempts(
        self, timeout: int = PAN_UPLOAD_ATTEMPTS_CACHE_TTL_IN_SECONDS
    ) -> int:
        """Increments the pan upload attempts.

        Args:
            timeout (int, optional): The timeout for the cache in seconds. Defaults to PAN_UPLOAD_ATTEMPTS_CACHE_TTL_IN_SECONDS.

        Returns:
            int: The new pan upload attempts count.
        """

        logger.info(
            "[PanCacheHandler] Incrementing pan_upload_attempts by 1 for key: %s with timeout: %s seconds."
            % (self._key, timeout)
        )
        return self.incr(timeout=timeout)
