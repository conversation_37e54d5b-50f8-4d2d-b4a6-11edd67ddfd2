import logging
from typing import Dict, Optional


from accounts.kyc.schemas import (
    AadhaarAddress,
    AadhaarDetail,
    KYCAddress,
    PanDetail,
)


from accounts.kyc.exceptions import (
    AadhaarParserException,
    KycFileDownloadParserException,
    PanOCRParserException,
)


logger = logging.getLogger(__name__)


class AadhaarParser:
    def __init__(self):
        self.aadhar_data: Optional[AadhaarDetail] = None

    def parse(self, data: dict) -> AadhaarDetail:
        if not data:
            raise AadhaarParserException(
                "[AadhaarParser] No or invalid data field provided."
            )

        address = data.get("address", {})

        self.aadhar_data = AadhaarDetail(
            client_id=data.get("client_id", ""),
            full_name=data.get("full_name", ""),
            aadhaar_number=data.get("aadhaar_number", ""),
            dob=data.get("dob", ""),
            gender=data.get("gender", ""),
            address=AadhaarAddress(
                country=address.get("country", ""),
                dist=address.get("dist", ""),
                state=address.get("state", ""),
                po=address.get("po", ""),
                loc=address.get("loc", ""),
                vtc=address.get("vtc", ""),
                subdist=address.get("subdist", ""),
                street=address.get("street", ""),
                house=address.get("house", ""),
                landmark=address.get("landmark", ""),
            ),
            face_status=data.get("face_status", False),
            face_score=data.get("face_score", -1),
            zip=data.get("zip", ""),
            profile_image=data.get("profile_image", ""),
            has_image=data.get("has_image", False),
            email_hash=data.get("email_hash", ""),
            mobile_hash=data.get("mobile_hash", ""),
            raw_xml=data.get("raw_xml", ""),
            zip_data=data.get("zip_data", ""),
            care_of=data.get("care_of", ""),
            share_code=data.get("share_code", ""),
            mobile_verified=data.get("mobile_verified", False),
            reference_id=data.get("reference_id", ""),
            aadhaar_pdf=data.get("aadhaar_pdf", None),
            status=data.get("status", ""),
            uniqueness_id=data.get("uniqueness_id", ""),
        )
        return self.aadhar_data

    def get_kyc_address(self) -> KYCAddress:
        if not self.aadhar_data or not self.aadhar_data.address:
            logger.error(
                "Error parsing address details: No Aadhaar data or address data"
            )
            return KYCAddress()
        address_data = self.aadhar_data.address
        address = f"{address_data.house} {address_data.street} {address_data.landmark}"
        return KYCAddress(
            address=address,
            area=address_data.loc,
            city=address_data.dist,
            state=address_data.state,
            pin_code=self.aadhar_data.zip,
        )


class AadhaarFileDownloadParser:
    def __init__(self, data: dict):
        self.data = data
        self.pdf_url = ""
        self.client_id = ""

    def get_pdf_url(self, data: Dict) -> str:
        url = data.get("aadhaar_pdf", "")
        if not url:
            raise KycFileDownloadParserException(
                "No PDF URL found in the Aadhaar PDF download API response"
            )
        return url

    def get_client_id(self, data: Dict) -> str:
        client_id = data.get("client_id", "")
        if not client_id:
            raise KycFileDownloadParserException(
                "No client id found in the Aadhaar PDF download API response"
            )
        return client_id

    def parse(self):
        if not self.data.get("data"):
            raise KycFileDownloadParserException(
                "No data found in the Aadhaar PDF download API response"
            )

        data = self.data.get("data", {})
        self.pdf_url = self.get_pdf_url(data)
        self.client_id = self.get_client_id(data)

        return self


class GstFileDownloadParser:
    def __init__(self, data: dict):
        self.data = data
        self.pdf_url = ""
        self.client_id = ""
        self.gstin = ""

    def get_pdf_url(self, data: Dict) -> str:
        url = data.get("pdf_report", "")
        if not url:
            raise KycFileDownloadParserException(
                "No PDF URL found in the response"
            )
        return url

    def get_client_id(self, data: Dict) -> str:
        client_id = data.get("client_id", "")
        if not client_id:
            raise KycFileDownloadParserException(
                "No client ID found in the response"
            )
        return client_id

    def get_gstin(self, data: Dict) -> str:
        gstin = data.get("gstin", "")
        if not gstin:
            raise KycFileDownloadParserException(
                "No GSTIN found in the response"
            )
        return gstin

    def parse(self):
        if not self.data.get("data"):
            raise KycFileDownloadParserException(
                "No data found in the response"
            )

        data = self.data.get("data", {})
        self.pdf_url = self.get_pdf_url(data)
        self.client_id = self.get_client_id(data)
        self.gstin = self.get_gstin(data)
        return self


class PanOCRParser:
    def __init__(self, raw_data: dict):
        self.raw_data = raw_data

    def get_client_id(self, data) -> str:
        client_id = data.get("client_id")
        if not client_id:
            raise PanOCRParserException(
                "[PanOCRParser] No or invalid client ID found in the PAN OCR API response."
            )
        return client_id

    def get_ocr_fields(self, data):
        ocr_fields = data.get("ocr_fields")
        if (not ocr_fields) or (not isinstance(ocr_fields, (list, tuple))):
            raise PanOCRParserException(
                "[PanOCRParser] No or invalid OCR fields found in the PAN OCR API response."
            )
        return ocr_fields

    def get_pan_number(self, ocr_fields):
        pan_number = ocr_fields.get("pan_number", {}).get("value")
        if not pan_number:
            raise PanOCRParserException(
                "[PanOCRParser] No or invalid PAN number found in the PAN OCR API response."
            )
        return pan_number

    def get_full_name(self, ocr_fields):
        full_name = ocr_fields.get("full_name", {}).get("value")
        if not full_name:
            raise PanOCRParserException(
                "[PanOCRParser] No or invalid full name found in the PAN OCR API response."
            )
        return full_name

    def get_father_name(self, ocr_fields):
        father_name = ocr_fields.get("father_name", {}).get("value")
        if not father_name:
            raise PanOCRParserException(
                "[PanOCRParser] No or invalid father name found in the PAN OCR API response."
            )
        return father_name

    def get_dob(self, ocr_fields):
        dob = ocr_fields.get("dob", {}).get("value")
        if not dob:
            raise PanOCRParserException(
                "[PanOCRParser] No or invalid DOB found in the PAN OCR API response."
            )
        return dob

    def parse(self) -> PanDetail:
        if not self.raw_data.get("data"):
            raise PanOCRParserException(
                "[PanOCRParser] No or invalid data found in the PAN OCR API response."
            )

        data = self.raw_data["data"]
        client_id = self.get_client_id(data)
        ocr_fields = self.get_ocr_fields(data)[0]
        pan_number = self.get_pan_number(ocr_fields)
        full_name = self.get_full_name(ocr_fields)
        father_name = self.get_father_name(ocr_fields)
        dob = self.get_dob(ocr_fields)

        return PanDetail(
            client_id=client_id,
            pan_number=pan_number,
            full_name=full_name,
            father_name=father_name,
            dob=dob,
        )
