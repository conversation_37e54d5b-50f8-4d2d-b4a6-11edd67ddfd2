import logging
import typing as t

from django.db.models import Q

from accounts.kyc.exceptions import KycNotFoundException
from accounts.kyc.models import KYC

logger = logging.getLogger(__name__)


class KycObjMixin:
    lookup_url_kwarg = "kyc_id"

    def get_object(
        self,
        request,
        kyc_status: t.Union[int, t.List[int], None] = None,
    ) -> KYC:
        """Fetches the KYC object from the database.

        Args:
            request (Request): The request object.
            kyc_status (t.Union[int, t.List[int], None], optional): The KYC status to filter by. Defaults to None.

        Raises:
            KycNotFoundException: If the KYC object does not exist.

        Returns:
            KYC: The KYC object.
        """

        kyc_id = self.kwargs[self.lookup_url_kwarg]  # type: ignore

        try:
            query = Q(id=kyc_id, billing_account=request.billing_account)
            if kyc_status:
                if isinstance(kyc_status, list) and len(kyc_status) > 0:
                    query &= Q(status__in=kyc_status)
                else:
                    query &= Q(status=kyc_status)

            return KYC.objects.get(query)
        except KYC.DoesNotExist:
            logger.error(
                f"[KycObjMixin] KYC not found for kyc_id: {kyc_id}, billing_account: {request.billing_account_id}"
            )
            raise KycNotFoundException()
