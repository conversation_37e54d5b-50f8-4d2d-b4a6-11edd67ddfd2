import logging

from django.forms.models import model_to_dict
from django.utils import timezone

from django_filters import rest_framework as django_filters
from rest_flex_fields import is_expanded
from rest_framework import generics, status
from rest_framework.exceptions import NotFound
from rest_framework.filters import OrderingFilter
from rest_framework.generics import ListAPIView
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework.views import APIView

from accounts import error_codes
from accounts.exceptions import BaseException
from accounts.generics import SnsHandlerView
from accounts.global_packages.filters import (
    HigherGlobalPackageFilter,
    LowerGlobalPackageFilter,
)
from accounts.global_packages.models import GlobalPackages
from accounts.global_packages.serializers import GlobalPackageSerializer
from accounts.kyc.exceptions import KycNotFoundException
from accounts.kyc.models import KYC
from accounts.kyc.serializers.kyc import KYCSerializer
from accounts.packages.serializers import PackageSerializer
from accounts.packages.utils.package_features import (
    process_paid_feature_activation,
)
from accounts.payments.enums import PaymentActionTypeEnum
from accounts.payments.models import PaymentActions
from accounts.services.exceptions import ServiceNotDemoException
from accounts.services.filters import ServiceFilter
from accounts.services.models import FeatureActivationRequest, Services
from accounts.services.serializers import (
    FeatureActivateListSerializer,
    FeatureActivationRequestSerializer,
    PaymentLinkSerializer,
    PendingUsagesResponseSerializer,
    ResourceChargeSerializer,
    ServiceActivationAmountResponseSerializer,
    ServiceExpandableSerializer,
    ServicePackageSerializer,
)
from accounts.services.tasks import (
    process_feature_payment,
    reactivate_service_feature,
    service_activation_task,
)
from accounts.services.utils.activation_payment import ActivationPaymentService
from accounts.services.utils.pending_usage import get_pending_usage
from accounts.services.utils.service_package import (
    get_current_and_upcoming_service_packages,
    get_current_service_package,
    get_service_packages,
)
from accounts.utils.helpers import get_root_billing_account_from_gsn

logger = logging.getLogger(__name__)


class ServiceActivationView(APIView):
    def post(self, request, gsn):
        try:
            service = Services.objects.get(gsn=gsn)
            if not service.is_demo():
                raise ServiceNotDemoException("Service is already activated")

            serializer = PaymentLinkSerializer(
                data=request.data, context={"service": service}
            )
            if serializer.is_valid(raise_exception=True):
                response_data = serializer.save()
                return Response(
                    {
                        "status": "success",
                        "message": "Activation Link Created",
                        "data": response_data,
                    }
                )
        except Services.DoesNotExist:
            raise NotFound(detail="Service not found", code=404)
        except ValidationError as e:
            return Response(
                {
                    "errors": e.detail,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ServiceActivationPayableAmountView(APIView):
    def post(self, request, gsn, *args, **kwargs):
        serializer = PaymentLinkSerializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            service = Services.objects.get(gsn=gsn)
            if not service:
                raise NotFound(detail="Service not found", code=404)

            payable_amount_details = ActivationPaymentService(
                service, serializer.data["package_id"]
            ).fetch_payable_amount()
            return Response(
                {
                    "message": "Payable Amount for Activation",
                    "data": ServiceActivationAmountResponseSerializer(
                        payable_amount_details
                    ).data,
                }
            )
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ServiceActivationEventListenerView(SnsHandlerView):
    def notification_handler(self, message):
        try:
            payment_id = message["payment_id"]
            payment_action = PaymentActions.objects.filter(
                payment_id=payment_id,
                action_type=PaymentActionTypeEnum.SERVICE_ACTIVATION.value,
            ).exists()
            if not payment_action:
                return Response(
                    {
                        "message": "payment is not for activation",
                        "data": {"task_id": None},
                    }
                )
            task = (
                service_activation_task.apply_async(
                    kwargs={
                        "payment_id": payment_id,
                    },
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="payment_id is missing")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ServicePackageRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = ServicePackageSerializer

    def get_queryset(self):
        return get_current_and_upcoming_service_packages(
            self.kwargs.get("gsn")
        ).all()

    def get(self, request, *args, **kwargs):
        try:
            if not Services.objects.filter(gsn=self.kwargs.get("gsn")).exists():
                raise NotFound(detail="Service not found", code=404)

            packages = self.get_queryset()
            fields = request.query_params.get("fields")
            if fields == "package":
                response_data = []
                for package in packages:
                    packages_serializer = self.get_serializer(package)
                    package_data = packages_serializer.data
                    package_instance = package.package
                    package_serializer = PackageSerializer(
                        package_instance, data=model_to_dict(package.package)
                    )
                    if package_serializer.is_valid(raise_exception=True):
                        package_data["package"] = package_serializer.data
                    response_data.append(package_data)
            else:
                response_data = self.get_serializer(packages, many=True).data

            if not response_data:
                response_data = None

            return Response(
                {
                    "status": "success",
                    "message": "" if response_data else "No packages",
                    "data": response_data,
                }
            )
        except BaseException as e:
            logger.title("Package Detail Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PackageSuggestionListView(generics.ListAPIView):
    serializer_class = GlobalPackageSerializer

    def get_queryset(self):
        gsn = self.kwargs.get("gsn")

        try:
            try:
                service = Services.objects.get(gsn=gsn)
            except Services.DoesNotExist:
                raise NotFound(detail="Service not found", code=404)

            service_package = get_current_service_package(service.id)

            rent_per_month = service_package.package.rent_per_month
            product_id = service.product.id
            package_category_id = service_package.package.package_category.id
            package_for = service_package.package.package_for

            if (
                rent_per_month is not None
                and package_category_id is not None
                and package_for is not None
            ):
                rent_per_month = float(rent_per_month)

                higher_packages = (
                    HigherGlobalPackageFilter(
                        self.request.query_params,
                        queryset=GlobalPackages.objects.all(),
                    )
                    .qs.filter(
                        rent_per_month__gt=rent_per_month,
                        product_id=product_id,
                        package_for=package_for,
                        package_category_id=package_category_id,
                        is_public=True,
                        status=True,
                        ocs_flag=2,
                    )
                    .order_by("-rent_per_month")[:3]
                )

                lower_packages = (
                    LowerGlobalPackageFilter(
                        self.request.query_params,
                        queryset=GlobalPackages.objects.all(),
                    )
                    .qs.filter(
                        rent_per_month__lt=rent_per_month,
                        product_id=product_id,
                        package_for=package_for,
                        package_category_id=package_category_id,
                        is_public=True,
                        status=True,
                        ocs_flag=2,
                    )
                    .order_by("rent_per_month")[:3]
                )

                return higher_packages, lower_packages

            else:
                return Response(
                    {
                        "errors": "rent_per_month, package_category_id, and package_for parameters are required",
                        "code": error_codes.VALIDATION_ERROR,
                    },
                    status.HTTP_400_BAD_REQUEST,
                )

        except BaseException as e:
            logger.title("Package Suggestion List Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        if queryset:
            higher_packages, lower_packages = queryset
            serializer = self.get_serializer(higher_packages, many=True)
            higher_data = serializer.data

            serializer = self.get_serializer(lower_packages, many=True)
            lower_data = serializer.data
        else:
            higher_data = None
            lower_data = None

        return Response(
            {
                "status": "success",
                "message": "List of package suggestions retrieved successfully",
                "data": {
                    "higher": higher_data if higher_data else None,
                    "lower": lower_data if lower_data else None,
                },
            }
        )


class CurrentServicePackageRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = ServicePackageSerializer

    def get_queryset(self):
        return get_service_packages(self.kwargs.get("gsn"))

    def get(self, request, *args, **kwargs):
        try:
            if not Services.objects.filter(gsn=self.kwargs.get("gsn")).exists():
                raise NotFound(detail="Service not found", code=404)
            response_data = None
            queryset = self.get_queryset()
            current_time = timezone.now()
            current_packages = queryset.filter(
                start_time__lte=current_time, end_time__gte=current_time
            ).first()
            if current_packages:
                current_serializer = self.get_serializer(current_packages)
                response_data = current_serializer.data
                fields = request.query_params.get("fields")
                if fields == "package":
                    package_instance = current_packages.package
                    package_serializer = PackageSerializer(
                        package_instance,
                        data=model_to_dict(current_packages.package),
                    )
                    if package_serializer.is_valid(raise_exception=True):
                        response_data["package"] = package_serializer.data

            return Response(
                {
                    "status": "success",
                    "message": "" if response_data else "No current package",
                    "data": response_data,
                }
            )
        except BaseException as e:
            logger.title("Current Package Detail Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class UpcomingServicePackageRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = ServicePackageSerializer

    def get_queryset(self):
        return get_service_packages(self.kwargs.get("gsn"))

    def get(self, request, *args, **kwargs):
        try:
            if not Services.objects.filter(gsn=self.kwargs.get("gsn")).exists():
                raise NotFound(detail="Service not found", code=404)
            response_data = None
            queryset = self.get_queryset()
            current_time = timezone.now()
            upcoming_packages = queryset.filter(
                start_time__gt=current_time
            ).first()
            if upcoming_packages:
                current_serializer = self.get_serializer(upcoming_packages)
                response_data = current_serializer.data
                fields = request.query_params.get("fields")
                if fields == "package":
                    package_instance = upcoming_packages.package
                    package_serializer = PackageSerializer(
                        package_instance,
                        data=model_to_dict(upcoming_packages.package),
                    )
                    if package_serializer.is_valid(raise_exception=True):
                        response_data["package"] = package_serializer.data

            return Response(
                {
                    "status": "success",
                    "message": "" if response_data else "No upcoming package",
                    "data": response_data,
                }
            )
        except BaseException as e:
            logger.title("Upcoming Package Detail Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PendingUsageRetrieveAPIView(generics.RetrieveAPIView):
    def get_queryset(self):
        return super().get_queryset()

    def get(self, request, gsn, *args, **kwargs):
        service = (
            Services.objects.select_related("billing_account")
            .filter(gsn=gsn)
            .first()
        )
        if not service:
            raise NotFound(detail="service not found", code=404)

        """
        Cache is disabled for now, it can be enabled after performance testing
        
        cache_param = request.query_params.get("cache", "").strip().lower()
        cached = True
        if cache_param in (
            "invalidate",
            "false",
        ):
            cached = False

        cache_handler = PendingUsageCacheHandler(service.billing_account)
        """
        try:
            """
            Cache is disabled for now, it can be enabled after performance testing

            if cached and cache_handler.exists():
                pending_usages = cache_handler.get()
            else:
                pending_usages = get_pending_usage(service)
            """
            pending_usages = get_pending_usage(service)
            return Response(
                {
                    "status": "success",
                    "message": "Pending Usages",
                    "data": PendingUsagesResponseSerializer(
                        pending_usages
                    ).data,
                }
            )
        except BaseException as e:
            logger.title("Pending Usage Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ResourceChargeCreateAPIView(generics.CreateAPIView):
    serializer_class = ResourceChargeSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        service = generics.get_object_or_404(
            Services, gsn=self.kwargs.get("gsn")
        )
        context["service"] = service
        return context

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        try:
            if serializer.context["service"].is_active() is False:
                raise ValidationError(detail="Service is inactive")

            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {
                    "status": "success",
                    "message": "Balance Deducted",
                }
            )
        except ValidationError as e:
            logger.title("Resource Charge Creation Validation Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "errors": e.detail,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Resource Charge Creation Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class FeatureActivationRequestCreateAPIView(generics.CreateAPIView):
    serializer_class = FeatureActivationRequestSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["gsn"] = self.kwargs.get("gsn")
        return context

    def post(self, request, gsn):
        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)

            # Bulk create entries in FeaturesActivationRequest table
            features_data = [
                FeatureActivationRequest(
                    service_id=serializer.validated_data["service_id"],
                    product_feature_id=feature["product_feature_id"],
                    ref_id=feature["ref_id"],
                    payment_id=serializer.validated_data["payment_id"],
                )
                for feature in serializer.validated_data["features"]
            ]
            FeatureActivationRequest.objects.bulk_create(features_data)

            return Response(
                {
                    "status": "success",
                    "message": "Features activated successfully",
                }
            )
        except ValidationError as e:
            logger.title("Feature Activation Request Validation Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "errors": e.detail,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Feature Activation Request Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class FeatureActivateCreateAPIView(generics.CreateAPIView):
    serializer_class = FeatureActivateListSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["gsn"] = self.kwargs.get("gsn")
        self._service = generics.get_object_or_404(
            Services, gsn=self.kwargs.get("gsn")
        )
        context["service"] = self._service
        return context

    def post(self, request, gsn):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            features = serializer.validated_data
            for feature_data in features:
                process_paid_feature_activation(
                    service=self._service,
                    gsn=gsn,
                    feature_data=feature_data,
                )

            return Response(
                {
                    "status": "success",
                    "message": "Features activated successfully",
                }
            )
        except ValidationError as e:
            logger.title("Feature Activation Request Validation Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "errors": e.detail,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Feature Activation Request Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class FeatureEventProcessView(SnsHandlerView):
    def notification_handler(self, message):
        try:
            payment_id = message["payment_id"]
            task = (
                process_feature_payment.apply_async(
                    kwargs={"payment_id": payment_id},
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="payment_id is missing")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ServiceReactivationEventProcessView(SnsHandlerView):
    def notification_handler(self, message):
        try:
            gsn = message["gsn"]
            task = (
                reactivate_service_feature.apply_async(
                    kwargs={"gsn": gsn},
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="params are missing")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ServiceListView(ListAPIView):
    serializer_class = ServiceExpandableSerializer
    filter_backends = [
        django_filters.DjangoFilterBackend,
        OrderingFilter,
    ]
    filterset_class = ServiceFilter
    ordering_fields = (
        "activation_date",
        "created",
    )
    ordering = "-created"  # default ordering asc
    queryset = Services.objects.all()

    def get_queryset(self):
        qs = super().get_queryset()
        if is_expanded(self.request, "billing_account"):
            qs = qs.select_related("billing_account")
        return qs


class ServiceBillingDetailsView(generics.RetrieveAPIView):
    lookup_field = "gsn"
    lookup_url_kwarg = "gsn"

    queryset = Services.objects.all()

    def get(self, request, *args, **kwargs):
        service: Services = self.get_object()
        billing_account, is_corporate = service.billing_account.parent_ban()
        data = {
            "billing_account_id": billing_account.id,
            "is_corporate": is_corporate,
            "ac_number": billing_account.ac_number,
        }
        return Response({"message": "Billing Account Info", "data": data})


class KycDetailApiView(generics.RetrieveAPIView):
    serializer_class = KYCSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        expand = self.request.query_params.get("expand", "")
        if expand and isinstance(
            expand, str
        ):  # expand is a string of fields to expand
            context["expand"] = expand.split(",")
        return context

    def get_object(self) -> KYC:
        gsn = self.kwargs.get("gsn")
        billing_account = get_root_billing_account_from_gsn(gsn)
        if not billing_account:
            raise KycNotFoundException("Invalid GSN provided.")
        kyc = (
            KYC.objects.filter(billing_account=billing_account)
            .order_by("-created")  # Using created to get the latest updated KYC
            .first()
        )
        if not kyc:
            raise KycNotFoundException("KYC details not found.")
        return kyc

    def get(self, request, *args, **kwargs):
        try:
            response = super().get(request, *args, **kwargs)

            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "KYC details fetched successfully",
                    "data": response.data,
                },
                status=status.HTTP_200_OK,
            )
        except BaseException as e:
            logger.title("KYC Detail Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
