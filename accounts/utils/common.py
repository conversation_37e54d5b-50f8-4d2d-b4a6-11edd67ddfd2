import calendar
import datetime
import hashlib
import random
import re
import time
import uuid as u
from typing import Any

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

import pytz
from dateutil import tz

from myoperator.centrallog import config


def uuid():
    uniqid = (
        hex(int(time.time()))[2:10]
        + hex(int(time.time() * 1000000) % 0x100000)[2:7]
    )
    rand = random.randint(111, 999)
    return str(uniqid) + str(rand)


def get_aadhaar_hash(aadhaar_no):
    return hashlib.md5(aadhaar_no.encode()).hexdigest()


def duration_to_seconds(duration):
    if not re.match(r"^(\d+d)?(\d+h)?(\d+m)?(\d+s)?$", duration):
        raise ValueError("Invalid duration format")

    days = hours = minutes = seconds = 0
    if "d" in duration:
        days, duration = duration.split("d")
    if "h" in duration:
        hours, duration = duration.split("h")
    if "m" in duration:
        minutes, duration = duration.split("m")
    if "s" in duration:
        seconds, duration = duration.split("s")
    return (
        int(days) * 86400 + int(hours) * 3600 + int(minutes) * 60 + int(seconds)
    )


def seconds_to_duration(seconds):
    duration = ""
    if seconds is None:
        return None
    days, seconds = divmod(seconds, 86400)
    hours, seconds = divmod(seconds, 3600)
    minutes, seconds = divmod(seconds, 60)

    if days > 0:
        duration += f"{days}d"
    if hours > 0:
        duration += f"{hours}h"
    if minutes > 0:
        duration += f"{minutes}m"
    if seconds > 0:
        duration += f"{seconds}s"

    return duration


def get_cache(key: str) -> Any:
    return cache.get(key)


def set_cache(key: str, value: Any, timeout: int = 3600) -> None:
    cache.set(key, value, timeout=timeout)


def invalidate_cache(key: str) -> None:
    cache.delete(key)


def convert_utc_to_local(
    date_time: timezone.datetime, time_zone: str
) -> timezone.datetime:
    tzone = tz.gettz(time_zone)
    local_datetime = date_time.replace(tzinfo=tz.gettz("UTC")).astimezone(tzone)
    return local_datetime


def convert_to_utc(
    local_datetime: timezone.datetime, time_zone: str
) -> timezone.datetime:
    # Convert the local datetime to a timezone-aware datetime with the specified time zone
    local_timezone = pytz.timezone(time_zone)
    local_datetime = local_timezone.localize(
        local_datetime
    )  # Make it timezone-aware if not already

    # Convert the local datetime to UTC
    utc_datetime = local_datetime.astimezone(pytz.UTC)

    return utc_datetime


def mask_number(number: str, mask_length: int = 3) -> str:
    if not number:
        return ""
    if len(number) <= mask_length:
        return "*" * len(number)
    return number[-mask_length:].rjust(len(number), "*")


def get_trace_id() -> str:
    """Returns UUID configured for CentralFormatter or generates a new one.

    Returns:
        str: The UUID.
    """

    cfg = config.get_config()
    trace_id = (cfg.uid if cfg else None) or u.uuid4()

    return str(trace_id)


def add_months(
    date: datetime.datetime, months: int, billing_day: int
) -> datetime.datetime:
    """
    Add months to a datetime object and return the new datetime object.
    """
    # Extract year and month
    y, m = date.year, date.month

    # Add months
    new_month = m + months
    while new_month > 12:
        new_month -= 12
        y += 1

    # Get last day of the new month
    last_day = calendar.monthrange(y, new_month)[1]

    # Adjust day if necessary
    d = min(billing_day, last_day)

    # Set new date
    dt = date.replace(year=y, month=new_month, day=d)
    return dt


def get_test_mobile_number_for_otp():
    return settings.TEST_MOBILE_NUMBER_FOR_OTP


def random_integer_unique():
    # Get current Unix time
    unixtime = int(time.mktime(time.localtime()))
    # Generate random integer between 1 and 10000
    randominteger = random.randint(1, 10000)
    # Concatenate Unix time and random integer
    return str(unixtime) + str(randominteger)
