import logging
import typing as t

from django.conf import settings

import boto3
import boto3.exceptions
from exceptions import S3ObjectNotFoundException

logger = logging.getLogger(__name__)
_S3_CLIENT = None


def get_s3_client():
    global _S3_CLIENT
    if _S3_CLIENT is None:
        _S3_CLIENT = boto3.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_DEFAULT_REGION,
        )
    return _S3_CLIENT


class S3Client:
    def __init__(self, bucket: str = settings.AWS_STORAGE_BUCKET_NAME):
        self.BUCKET = bucket
        self.region = settings.AWS_DEFAULT_REGION
        self._s3_client = None

    def get_client(self):
        if not self._s3_client:
            self._s3_client = get_s3_client()
        return self._s3_client

    def create_presigned_url(
        self, object_name: str, expiration: int, method="put_object"
    ):
        """Generate a presigned URL to PUT S3 object

        :param bucket_name: string
        :param object_name: string
        :param expiration: Time in seconds for the presigned URL to remain valid
        :param method: Method for which presigned URL is being generated, Tested for ["put_object", "get_object"]
        :return: Presigned URL as string. If error, returns None.
        """

        try:
            response = self.get_client().generate_presigned_url(
                method,
                Params={"Bucket": self.BUCKET, "Key": object_name},
                ExpiresIn=expiration,
            )
            logger.info(response, title="Presigned S3 URL")

        except Exception as err:
            logger.critical(
                object_name,
                title="Unable to create Presigned S3 URL",
                exc_info=True,
            )
            raise err
        # The response contains the presigned URL
        return response

    def generate_presigned_post(
        self,
        key: str,
        content_type: str,
        expiration: int,
        max_file_size: int = settings.PRESIGNED_POST_FILE_UPLOAD_MAX_SIZE_IN_BYTES,
    ) -> dict:
        """Generates a presigned post to PUT S3 object.

        Args:
            key (str): The key (file name with extension) for the object.
            content_type (str): The content type of the object.
            expiration (int): The expiration time of the presigned post in seconds.
            max_file_size (int, optional): The maximum file size in bytes. Defaults to settings.PRESIGNED_POST_FILE_UPLOAD_MAX_SIZE_IN_BYTES.

        Raises:
            Exception: If an error occurs while generating the presigned post.

        Returns:
            dict: The presigned post data.
            e.g.:
                {
                    "url": "https://s3.ap-south-1.amazonaws.com/<bucket_name>",
                    "fields": {
                        "Content-Type": "<content_type>",
                        "key": "<key>", # tmp/companydoc/abc.png
                        "x-amz-algorithm": "AWS4-HMAC-SHA256",
                        "x-amz-credential": "<access_key_id>/<YYYYMMDD>/<region>/s3/aws4_request",
                        "x-amz-date": "<YYYYMMDDTHHMMSSZ>", # 20250619T130653Z
                        "policy": "<policy>",
                        "x-amz-signature": "<signature>"
                    }
                }
        """

        try:
            response = self.get_client().generate_presigned_post(
                Bucket=self.BUCKET,
                Key=key,
                Fields={"Content-Type": content_type},
                Conditions=[
                    {"Content-Type": content_type},
                    ["content-length-range", 0, max_file_size],
                ],
                ExpiresIn=expiration,
            )
            logger.info(response, title="Presigned post S3")
            return response
        except Exception as e:
            error_msg = "%s: %s" % (e.__class__.__name__, str(e))
            logger.critical(
                "Failed to generate Presigned post S3. Exc: %s" % error_msg,
                exc_info=True,
                title="Failed to generate Presigned post S3",
            )
            raise e

    def upload_file_obj(self, body: object, key: str, **kwargs):
        """
        Function to upload a given file to an S3 bucket
        """
        try:
            self.get_client().put_object(
                Bucket=self.BUCKET, Key=key, Body=body, **kwargs
            )

            logger.info(key, title="Uploaded file to S3")
            return f"https://{self.BUCKET}.s3.{self.region}.amazonaws.com/{key}"
        except Exception as error:
            logger.critical(
                key, title="Uploading file to S3 failed", exc_info=True
            )
            raise error

    def download(self, key: str):
        try:
            res = self.get_client().get_object(Bucket=self.BUCKET, Key=key)
            logger.info(
                "key: %s, res: %s" % (key, res), title="Downloaded from S3"
            )

            if not res.get("Body"):
                raise S3ObjectNotFoundException("S3 Object body not found")
            return res
        except Exception as err:
            logger.error(key, title="Downloading from S3 failed", exc_info=True)
            raise err

    def head_object(self, key: str):
        try:
            res = self.get_client().head_object(Bucket=self.BUCKET, Key=key)
            logger.info(key, title="Object data fetched successful")
            return res
        except Exception as err:
            logger.critical(
                key, title="Object data fetch failed", exc_info=True
            )
            raise err

    def copy_file_object(self, file_source: str, file_destination: str):
        logger.info(
            "Copying file within S3 file_source: %s, file_destination: %s"
            % (file_source, file_destination)
        )
        copy_source = {
            "Bucket": self.BUCKET,
            "Key": file_source,
        }
        try:
            res = self.get_client().copy(
                CopySource=copy_source, Bucket=self.BUCKET, Key=file_destination
            )
            logger.info(res, title="Copy file within S3 successful")
            return res
        except Exception as err:
            logger.critical(
                "Copying file within S3 failed",
                title="Copy file within S3 failed",
                exc_info=True,
            )
            raise err


def get_file_name_from_s3_object_key(obj_key: str) -> str:
    return obj_key.split("/")[-1]


def get_s3_file_url(bucket_name: str, key: str):
    return f"https://{bucket_name}.s3.{settings.AWS_S3_REGION_NAME}.amazonaws.com/{key}"
