import logging

from accounts.billing_accounts.exceptions import InvalidGstNumberException
from accounts.billing_accounts.utils.gst_parser import GSTDetail
from accounts.exceptions import ExternalApiDependencyFailedException
from accounts.kyc.exceptions import InactiveGstNumberException
from accounts.utils.api_services.surepass import SurepassApi

logger = logging.getLogger(__name__)


def get_gst_data(gst_number: str) -> GSTDetail:
    try:
        gst_data = SurepassApi().gstin_details(gst_number)
        if not gst_data.is_active():
            logger.error(f"Inactive GST number: {gst_number}")
            raise InactiveGstNumberException()
        return gst_data
    except InvalidGstNumberException as e:
        logger.error(f"Invalid GST number: {e} ", exc_info=True)
        raise ExternalApiDependencyFailedException(
            "GST details fetch failed. Please try again later.",
            errors=str(e),
        ) from e
