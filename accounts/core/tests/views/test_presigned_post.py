import mimetypes

from django.conf import settings
from django.urls import reverse

import pytest
from core.constants import PAN_CONFIG
from freezegun import freeze_time
from rest_framework import status


@pytest.fixture
def presigned_post_url():
    return reverse("core:pre-signed-post")


@pytest.mark.parametrize(
    "request_data",
    [
        {},
        {"module": None},
        {"module": ""},
        {"module": "invalidmodule"},
        {"module": "invalid_module"},
        {"module": "kyc_pan"},
        {"module": "kyc_pan", "content_type": None},
        {"module": "kyc_pan", "content_type": ""},
        {"module": "kyc_pan", "content_type": "invalid_content_type"},
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_pre_signed_post_with_invalid_request_returns_http_400_bad_request(
    client, presigned_post_url, request_data
):
    """Test that the API returns a 400 status code and the correct response when the request is invalid."""

    response = client.post(
        presigned_post_url,
        request_data,
        content_type="application/json",
    )
    # assert API response
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_json = response.json()
    assert response_json["status"] == "error"
    assert response_json["code"] == status.HTTP_400_BAD_REQUEST
    assert (
        response_json["message"]
        == "Validation Failed, Please check errors field for details."
    )
    assert response_json["errors"]


def test_pre_signed_post_with_valid_request_returns_http_200_ok(
    client, presigned_post_url, mock_s3_bucket
):
    """Test that the API returns a 200 status code and the correct response when the request is valid."""

    content_type = "image/jpeg"
    file_name_with_ext = "my_file_name.jpg"
    response = client.post(
        presigned_post_url,
        {
            "module": "kyc_pan",
            "content_type": content_type,
            "file_name": file_name_with_ext,
        },
        content_type="application/json",
    )
    # assert API response
    s3_key = f"{PAN_CONFIG['S3_TMP_PREFIX']}{file_name_with_ext}"
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["status"] == "success"
    assert response_json["code"] == status.HTTP_200_OK
    assert response_json["message"] == "Presigned post generated successfully."
    response_json_data = response_json["data"]
    assert (
        response_json_data["presigned_post_data"]["url"]
        == f"https://{settings.AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/"
    )
    assert (
        response_json_data["presigned_post_data"]["fields"]["Content-Type"]
        == content_type
    )
    assert response_json_data["presigned_post_data"]["fields"]["key"] == s3_key
    assert response_json_data["presigned_post_data"]["fields"][
        "x-amz-algorithm"
    ]
    assert response_json_data["presigned_post_data"]["fields"][
        "x-amz-credential"
    ]
    assert response_json_data["presigned_post_data"]["fields"]["x-amz-date"]
    assert response_json_data["presigned_post_data"]["fields"]["policy"]
    assert response_json_data["presigned_post_data"]["fields"][
        "x-amz-signature"
    ]
    assert response_json_data["s3_key"] == s3_key
    assert response_json_data["file_name"] == file_name_with_ext
    assert (
        response_json_data["expiration"]
        == settings.PRESIGNED_URL_EXPIRATION_IN_SECONDS
    )


@freeze_time("2023-04-01T18:35:00Z")
def test_pre_signed_post_with_valid_request_without_file_name_returns_http_200_ok(
    client, presigned_post_url, mock_s3_bucket
):
    """Test that the API returns a 200 status code and the correct response when the request is valid."""

    content_type = "image/jpeg"
    file_name_with_ext = f"1680374100{mimetypes.guess_extension(content_type)}"
    response = client.post(
        presigned_post_url,
        {
            "module": "kyc_pan",
            "content_type": content_type,
        },
        content_type="application/json",
    )
    # assert API response
    s3_key = f"{PAN_CONFIG['S3_TMP_PREFIX']}{file_name_with_ext}"
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["status"] == "success"
    assert response_json["code"] == status.HTTP_200_OK
    assert response_json["message"] == "Presigned post generated successfully."
    response_json_data = response_json["data"]
    assert (
        response_json_data["presigned_post_data"]["url"]
        == f"https://{settings.AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com/"
    )
    assert (
        response_json_data["presigned_post_data"]["fields"]["Content-Type"]
        == content_type
    )
    assert response_json_data["presigned_post_data"]["fields"]["key"] == s3_key
    assert response_json_data["presigned_post_data"]["fields"][
        "x-amz-algorithm"
    ]
    assert response_json_data["presigned_post_data"]["fields"][
        "x-amz-credential"
    ]
    assert response_json_data["presigned_post_data"]["fields"]["x-amz-date"]
    assert response_json_data["presigned_post_data"]["fields"]["policy"]
    assert response_json_data["presigned_post_data"]["fields"][
        "x-amz-signature"
    ]
    assert response_json_data["s3_key"] == s3_key
    assert response_json_data["file_name"] == file_name_with_ext
    assert (
        response_json_data["expiration"]
        == settings.PRESIGNED_URL_EXPIRATION_IN_SECONDS
    )
