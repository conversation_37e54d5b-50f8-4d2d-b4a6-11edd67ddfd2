import mimetypes
from unittest.mock import patch

from django.conf import settings

import pytest
from core.constants import PAN_CONFIG
from freezegun import freeze_time

from accounts.core.validators import PresignedUrlInputValidator


@pytest.mark.parametrize(
    "module_string",
    ["", "invalidmodule", "invalid_module"],
)
@pytest.mark.unittest
def test_presigned_url_input_validator_with_invalid_module_string(
    module_string,
):
    """Test initialization with invalid module string."""

    with pytest.raises(ValueError):
        PresignedUrlInputValidator(module_string=module_string, content_type="")


@pytest.mark.parametrize(
    "content_type",
    ["", "invalid_content_type"],
)
@pytest.mark.unittest
def test_presigned_url_input_validator_with_invalid_content_type(
    content_type, core_module_string_kyc_pan
):
    """Test validate with invalid content type."""

    with pytest.raises(ValueError):
        PresignedUrlInputValidator(
            module_string=core_module_string_kyc_pan, content_type=content_type
        ).validate()


@pytest.mark.parametrize(
    "content_type",
    settings.PAN["ALLOWED_CONTENT_TYPES"],
)
@pytest.mark.unittest
def test_presigned_url_input_validator_validate_success(
    content_type, core_module_string_kyc_pan
):
    """Test validate for successful validation."""

    file_extension = content_type.split("/")[1]
    file_name = f"my_file_name.{file_extension}"
    validator = PresignedUrlInputValidator(
        module_string=core_module_string_kyc_pan,
        content_type=content_type,
        file_name=file_name,
    )

    validator.validate()

    assert validator.module == "kyc"
    assert validator.submodule == "pan"
    assert validator.config == PAN_CONFIG
    assert validator.content_type == content_type
    assert validator.file_name == file_name
    assert validator.final_file_name == file_name
    assert (
        validator.file_size
        == PAN_CONFIG["DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES"]
    )


@freeze_time("2023-04-01T18:35:00Z")
@pytest.mark.parametrize(
    "content_type",
    settings.PAN["ALLOWED_CONTENT_TYPES"],
)
@pytest.mark.unittest
def test_presigned_url_input_validator_validate_success_without_file_name(
    content_type, core_module_string_kyc_pan
):
    """Test validate for successful validation without file name."""

    validator = PresignedUrlInputValidator(
        module_string=core_module_string_kyc_pan,
        content_type=content_type,
    )

    validator.validate()

    assert validator.module == "kyc"
    assert validator.submodule == "pan"
    assert validator.config == PAN_CONFIG
    assert validator.content_type == content_type
    assert validator.file_name is None
    assert (
        validator.final_file_name
        == f"1680374100{mimetypes.guess_extension(content_type)}"
    )
    assert (
        validator.file_size
        == PAN_CONFIG["DEFAULT_FILE_UPLOAD_MAX_SIZE_IN_BYTES"]
    )


def test_presigned_url_input_validator_with_valid_module_string_case_insensitive(
    core_module_string_kyc_pan,
):
    """Test initialization with valid module string supports case insensitive."""

    validator = PresignedUrlInputValidator(
        module_string=core_module_string_kyc_pan.lower(), content_type=""
    )

    assert validator.module == "kyc"
    assert validator.submodule == "pan"

    validator = PresignedUrlInputValidator(
        module_string=core_module_string_kyc_pan.upper(), content_type=""
    )

    assert validator.module == "kyc"
    assert validator.submodule == "pan"


@pytest.mark.parametrize(
    "content_type",
    settings.PAN["ALLOWED_CONTENT_TYPES"],
)
def test_presigned_url_input_validator_with_file_name_extension_mismatch_with_content_type(
    content_type, core_module_string_kyc_pan
):
    """Test validate with file name extension mismatch with content type."""

    with pytest.raises(ValueError):
        PresignedUrlInputValidator(
            module_string=core_module_string_kyc_pan,
            content_type=content_type,
            file_name="my_file_name.some_other_extension",
        ).validate()
