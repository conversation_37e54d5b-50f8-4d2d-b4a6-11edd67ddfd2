import logging

from django.conf import settings

from rest_framework import filters, generics, status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.views import APIView
from utils.aws.s3 import S3Client

from accounts.core.models import (
    Countries,
    StateCodes,
)
from accounts.core.serializers import (
    CountryStateSerializer,
    PresignedUrlRequestSerializer,
)
from accounts.exceptions import BaseException

logger = logging.getLogger(__name__)


class CountryStatesListView(generics.ListAPIView):
    serializer_class = CountryStateSerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ["id", "name"]
    ordering = ["name"]  # default ordering

    def get_queryset(self):
        country_id = self.kwargs.get("id")
        if not Countries.objects.filter(id=country_id).exists():
            raise NotFound(detail="Invalid Country ID")
        return StateCodes.objects.filter(country_id=country_id)

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())

            data = self.paginate_queryset(queryset)
            serializer = self.get_serializer(data, many=True)
            return self.get_paginated_response(serializer.data)
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PresignedPostView(APIView):
    def post(self, request):
        """
        Generates a presigned post for S3 object upload.

        Request Body:
            module (str): The module for which the presigned post is being generated. e.g. kyc_pan, etc.
            content_type (str): The content type of the object. e.g. image/jpeg, image/png, etc.
            file_name (str, optional): The file name of the object. If not provided, a random file name as epoch will be generated.
        """

        logger.info("PresignedPostView POST request.data: %s" % request.data)
        serializer = PresignedUrlRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "status": "error",
                    "code": status.HTTP_400_BAD_REQUEST,
                    "message": "Validation Failed, Please check errors field for details.",
                    "errors": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        content_type = serializer.validated_data["content_type"]
        s3_tmp_prefix = serializer.validated_data["s3_tmp_prefix"]
        file_name = serializer.validated_data["file_name"]
        file_size = serializer.validated_data["file_size"]
        s3_key = f"{s3_tmp_prefix}{file_name}"

        try:
            presigned_post_data = S3Client().generate_presigned_post(
                key=s3_key,
                content_type=content_type,
                expiration=settings.PRESIGNED_URL_EXPIRATION_IN_SECONDS,
                max_file_size=file_size,
            )
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "Presigned post generated successfully.",
                    "data": {
                        "presigned_post_data": presigned_post_data,
                        "s3_key": s3_key,
                        "file_name": file_name,
                        "expiration": settings.PRESIGNED_URL_EXPIRATION_IN_SECONDS,
                    },
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            error_msg = "%s: %s" % (e.__class__.__name__, str(e))
            return Response(
                {
                    "status": "error",
                    "code": status.HTTP_500_INTERNAL_SERVER_ERROR,
                    "message": "Failed to generate Presigned post.",
                    "errors": {"detail": error_msg},
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
